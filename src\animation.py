"""
Professional Animation System - Dead Cells Style
"""

import pygame
import math
from src.constants import *

class Animation:
    def __init__(self, frames, frame_duration=0.1, loop=True):
        """Initialize animation"""
        self.frames = frames
        self.frame_duration = frame_duration
        self.loop = loop
        self.current_frame = 0
        self.timer = 0
        self.finished = False
    
    def update(self, dt):
        """Update animation"""
        if self.finished and not self.loop:
            return
        
        self.timer += dt
        if self.timer >= self.frame_duration:
            self.timer = 0
            self.current_frame += 1
            
            if self.current_frame >= len(self.frames):
                if self.loop:
                    self.current_frame = 0
                else:
                    self.current_frame = len(self.frames) - 1
                    self.finished = True
    
    def get_current_frame(self):
        """Get current animation frame"""
        return self.frames[self.current_frame]
    
    def reset(self):
        """Reset animation to beginning"""
        self.current_frame = 0
        self.timer = 0
        self.finished = False


class AnimationManager:
    def __init__(self, sprite_manager):
        """Initialize animation manager"""
        self.sprite_manager = sprite_manager
        self.animations = {}
        self.create_animations()
    
    def create_animations(self):
        """Create all character animations"""
        
        # Player animations
        player_idle = Animation([self.sprite_manager.get_sprite('player')], 1.0)
        player_walk = Animation([
            self.sprite_manager.get_sprite('player'),
            self.sprite_manager.get_sprite('player_walk1'),
            self.sprite_manager.get_sprite('player'),
            self.sprite_manager.get_sprite('player_walk2')
        ], 0.15)
        
        self.animations['player_idle'] = player_idle
        self.animations['player_walk'] = player_walk
        
        # Zombie animations
        zombie_idle = Animation([self.sprite_manager.get_sprite(ENTITY_ZOMBIE)], 1.0)
        zombie_walk = Animation([
            self.sprite_manager.get_sprite(ENTITY_ZOMBIE),
            self.sprite_manager.get_sprite('zombie_walk1'),
            self.sprite_manager.get_sprite(ENTITY_ZOMBIE),
            self.sprite_manager.get_sprite('zombie_walk2')
        ], 0.25)  # Slower zombie animation
        
        self.animations['zombie_idle'] = zombie_idle
        self.animations['zombie_walk'] = zombie_walk
    
    def get_animation(self, name):
        """Get animation by name"""
        return self.animations.get(name, None)
    
    def update_all(self, dt):
        """Update all animations"""
        for animation in self.animations.values():
            animation.update(dt)


class ParticleSystem:
    def __init__(self, sprite_manager):
        """Initialize particle system"""
        self.sprite_manager = sprite_manager
        self.particles = []
    
    def add_particle(self, x, y, particle_type, velocity_x=0, velocity_y=0, lifetime=1.0):
        """Add a particle"""
        particle = {
            'x': x,
            'y': y,
            'vel_x': velocity_x,
            'vel_y': velocity_y,
            'lifetime': lifetime,
            'max_lifetime': lifetime,
            'type': particle_type,
            'sprite': self.sprite_manager.get_sprite(particle_type)
        }
        self.particles.append(particle)
    
    def add_blood_splash(self, x, y):
        """Add blood splash effect"""
        for i in range(8):
            angle = (i / 8) * 2 * math.pi
            vel_x = math.cos(angle) * 50
            vel_y = math.sin(angle) * 50
            self.add_particle(x, y, 'blood_particle', vel_x, vel_y, 0.5)
    
    def add_dust_cloud(self, x, y):
        """Add dust cloud effect"""
        for i in range(5):
            vel_x = (i - 2) * 20
            vel_y = -30 - i * 10
            self.add_particle(x, y, 'dust_particle', vel_x, vel_y, 0.8)
    
    def add_sparkle_effect(self, x, y):
        """Add sparkle effect"""
        self.add_particle(x, y, 'sparkle', 0, -20, 0.6)
    
    def update(self, dt):
        """Update all particles"""
        particles_to_remove = []
        
        for particle in self.particles:
            # Update position
            particle['x'] += particle['vel_x'] * dt
            particle['y'] += particle['vel_y'] * dt
            
            # Apply gravity to some particles
            if particle['type'] in ['blood_particle', 'dust_particle']:
                particle['vel_y'] += 200 * dt  # Gravity
            
            # Update lifetime
            particle['lifetime'] -= dt
            if particle['lifetime'] <= 0:
                particles_to_remove.append(particle)
        
        # Remove dead particles
        for particle in particles_to_remove:
            self.particles.remove(particle)
    
    def render(self, screen, camera):
        """Render all particles"""
        for particle in self.particles:
            if particle['sprite']:
                screen_x = particle['x'] - camera.x
                screen_y = particle['y'] - camera.y
                
                # Fade out based on lifetime
                alpha = int((particle['lifetime'] / particle['max_lifetime']) * 255)
                sprite = particle['sprite'].copy()
                sprite.set_alpha(alpha)
                
                screen.blit(sprite, (screen_x, screen_y))


class ScreenEffects:
    def __init__(self):
        """Initialize screen effects"""
        self.shake_intensity = 0
        self.shake_duration = 0
        self.shake_timer = 0
        
        self.flash_color = (255, 255, 255)
        self.flash_intensity = 0
        self.flash_duration = 0
        self.flash_timer = 0
    
    def add_screen_shake(self, intensity, duration):
        """Add screen shake effect"""
        self.shake_intensity = intensity
        self.shake_duration = duration
        self.shake_timer = 0
    
    def add_screen_flash(self, color, intensity, duration):
        """Add screen flash effect"""
        self.flash_color = color
        self.flash_intensity = intensity
        self.flash_duration = duration
        self.flash_timer = 0
    
    def update(self, dt):
        """Update screen effects"""
        # Update shake
        if self.shake_timer < self.shake_duration:
            self.shake_timer += dt
        else:
            self.shake_intensity = 0
        
        # Update flash
        if self.flash_timer < self.flash_duration:
            self.flash_timer += dt
        else:
            self.flash_intensity = 0
    
    def get_shake_offset(self):
        """Get current shake offset"""
        if self.shake_intensity <= 0:
            return (0, 0)
        
        import random
        progress = self.shake_timer / self.shake_duration if self.shake_duration > 0 else 1
        current_intensity = self.shake_intensity * (1 - progress)
        
        offset_x = random.randint(-int(current_intensity), int(current_intensity))
        offset_y = random.randint(-int(current_intensity), int(current_intensity))
        
        return (offset_x, offset_y)
    
    def render_flash(self, screen):
        """Render screen flash"""
        if self.flash_intensity <= 0:
            return
        
        progress = self.flash_timer / self.flash_duration if self.flash_duration > 0 else 1
        current_intensity = self.flash_intensity * (1 - progress)
        
        if current_intensity > 0:
            flash_surface = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            flash_surface.fill(self.flash_color)
            flash_surface.set_alpha(int(current_intensity))
            screen.blit(flash_surface, (0, 0))


class DamageNumbers:
    def __init__(self, sprite_manager):
        """Initialize damage number system"""
        self.sprite_manager = sprite_manager
        self.damage_numbers = []
        self.font = pygame.font.Font(None, 24)
    
    def add_damage_number(self, x, y, damage, color=(255, 100, 100)):
        """Add floating damage number"""
        damage_num = {
            'x': x,
            'y': y,
            'damage': damage,
            'color': color,
            'lifetime': 1.5,
            'vel_y': -50
        }
        self.damage_numbers.append(damage_num)
    
    def update(self, dt):
        """Update damage numbers"""
        numbers_to_remove = []
        
        for num in self.damage_numbers:
            num['y'] += num['vel_y'] * dt
            num['vel_y'] += 20 * dt  # Slow down upward movement
            num['lifetime'] -= dt
            
            if num['lifetime'] <= 0:
                numbers_to_remove.append(num)
        
        for num in numbers_to_remove:
            self.damage_numbers.remove(num)
    
    def render(self, screen, camera):
        """Render damage numbers"""
        for num in self.damage_numbers:
            screen_x = num['x'] - camera.x
            screen_y = num['y'] - camera.y
            
            # Fade out
            alpha = int((num['lifetime'] / 1.5) * 255)
            
            # Render damage text
            text = self.font.render(str(int(num['damage'])), True, num['color'])
            text.set_alpha(alpha)
            
            # Add background for readability
            bg_sprite = self.sprite_manager.get_sprite('damage_bg')
            if bg_sprite:
                bg_sprite.set_alpha(alpha // 2)
                screen.blit(bg_sprite, (screen_x - 16, screen_y - 8))
            
            screen.blit(text, (screen_x - 8, screen_y - 8))
