"""
Crafting system for the game
"""

from src.constants import *

class Recipe:
    def __init__(self, result_item, result_amount, ingredients):
        """Initialize a crafting recipe"""
        self.result_item = result_item
        self.result_amount = result_amount
        self.ingredients = ingredients  # Dict of {item_type: amount_needed}
    
    def can_craft(self, inventory):
        """Check if recipe can be crafted with current inventory"""
        for item_type, amount_needed in self.ingredients.items():
            if inventory.get(item_type, 0) < amount_needed:
                return False
        return True
    
    def craft(self, inventory):
        """Craft the item, consuming ingredients"""
        if not self.can_craft(inventory):
            return False
        
        # Consume ingredients
        for item_type, amount_needed in self.ingredients.items():
            inventory[item_type] -= amount_needed
            if inventory[item_type] <= 0:
                del inventory[item_type]
        
        # Add result to inventory
        if self.result_item in inventory:
            inventory[self.result_item] += self.result_amount
        else:
            inventory[self.result_item] = self.result_amount
        
        return True


class CraftingSystem:
    def __init__(self):
        """Initialize crafting system"""
        self.recipes = {}
        self.setup_recipes()
    
    def setup_recipes(self):
        """Setup all crafting recipes"""
        # Basic items
        self.add_recipe(ITEM_STICK, 4, {BLOCK_WOOD: 2})
        self.add_recipe(BLOCK_PLANKS, 4, {BLOCK_WOOD: 1})
        self.add_recipe(BLOCK_COBBLESTONE, 1, {BLOCK_STONE: 1})
        
        # Tools - Wood
        self.add_recipe(TOOL_AXE_WOOD, 1, {BLOCK_WOOD: 3, ITEM_STICK: 2})
        self.add_recipe(TOOL_PICKAXE_WOOD, 1, {BLOCK_WOOD: 3, ITEM_STICK: 2})
        self.add_recipe(TOOL_SWORD_WOOD, 1, {BLOCK_WOOD: 2, ITEM_STICK: 1})
        
        # Tools - Stone
        self.add_recipe(TOOL_AXE_STONE, 1, {BLOCK_COBBLESTONE: 3, ITEM_STICK: 2})
        self.add_recipe(TOOL_PICKAXE_STONE, 1, {BLOCK_COBBLESTONE: 3, ITEM_STICK: 2})
        self.add_recipe(TOOL_SWORD_STONE, 1, {BLOCK_COBBLESTONE: 2, ITEM_STICK: 1})
        
        # Tools - Iron
        self.add_recipe(TOOL_AXE_IRON, 1, {ITEM_IRON: 3, ITEM_STICK: 2})
        self.add_recipe(TOOL_PICKAXE_IRON, 1, {ITEM_IRON: 3, ITEM_STICK: 2})
        self.add_recipe(TOOL_SWORD_IRON, 1, {ITEM_IRON: 2, ITEM_STICK: 1})
        
        # Processed materials
        self.add_recipe(ITEM_IRON, 1, {BLOCK_IRON_ORE: 1, ITEM_COAL: 1})
        self.add_recipe(ITEM_COAL, 1, {BLOCK_COAL_ORE: 1})
        
        # Food (simple cooking)
        self.add_recipe(ITEM_FOOD, 2, {ITEM_COAL: 1})  # "Cooked" food
    
    def add_recipe(self, result_item, result_amount, ingredients):
        """Add a recipe to the system"""
        recipe = Recipe(result_item, result_amount, ingredients)
        self.recipes[result_item] = recipe
    
    def get_recipe(self, item_type):
        """Get recipe for an item"""
        return self.recipes.get(item_type, None)
    
    def get_all_recipes(self):
        """Get all available recipes"""
        return list(self.recipes.values())
    
    def get_craftable_recipes(self, inventory):
        """Get recipes that can be crafted with current inventory"""
        craftable = []
        for recipe in self.recipes.values():
            if recipe.can_craft(inventory):
                craftable.append(recipe)
        return craftable
    
    def craft_item(self, item_type, inventory):
        """Craft an item if possible"""
        recipe = self.get_recipe(item_type)
        if recipe and recipe.can_craft(inventory):
            return recipe.craft(inventory)
        return False
    
    def get_item_name(self, item_type):
        """Get display name for item type"""
        names = {
            # Blocks
            BLOCK_GRASS: "Grass Block",
            BLOCK_DIRT: "Dirt Block",
            BLOCK_STONE: "Stone Block",
            BLOCK_WOOD: "Wood Block",
            BLOCK_LEAVES: "Leaves",
            BLOCK_WATER: "Water",
            BLOCK_SAND: "Sand Block",
            BLOCK_COBBLESTONE: "Cobblestone",
            BLOCK_PLANKS: "Wood Planks",
            BLOCK_COAL_ORE: "Coal Ore",
            BLOCK_IRON_ORE: "Iron Ore",
            
            # Items
            ITEM_WOOD: "Wood",
            ITEM_STONE: "Stone",
            ITEM_STICK: "Stick",
            ITEM_COAL: "Coal",
            ITEM_IRON: "Iron Ingot",
            ITEM_FOOD: "Food",
            
            # Tools
            TOOL_AXE_WOOD: "Wooden Axe",
            TOOL_AXE_STONE: "Stone Axe",
            TOOL_AXE_IRON: "Iron Axe",
            TOOL_PICKAXE_WOOD: "Wooden Pickaxe",
            TOOL_PICKAXE_STONE: "Stone Pickaxe",
            TOOL_PICKAXE_IRON: "Iron Pickaxe",
            TOOL_SWORD_WOOD: "Wooden Sword",
            TOOL_SWORD_STONE: "Stone Sword",
            TOOL_SWORD_IRON: "Iron Sword",
        }
        return names.get(item_type, f"Item {item_type}")
    
    def render_crafting_menu(self, screen, font, small_font, inventory, selected_recipe=0):
        """Render crafting menu"""
        # Background
        menu_rect = pygame.Rect(SCREEN_WIDTH // 4, SCREEN_HEIGHT // 4, 
                               SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
        pygame.draw.rect(screen, DARK_GRAY, menu_rect)
        pygame.draw.rect(screen, WHITE, menu_rect, 3)
        
        # Title
        title = font.render("CRAFTING", True, WHITE)
        title_rect = title.get_rect(centerx=menu_rect.centerx, y=menu_rect.y + 10)
        screen.blit(title, title_rect)
        
        # Get craftable recipes
        craftable_recipes = self.get_craftable_recipes(inventory)
        all_recipes = self.get_all_recipes()
        
        # Recipe list
        y_offset = menu_rect.y + 50
        max_recipes = 8
        
        for i, recipe in enumerate(all_recipes[:max_recipes]):
            color = GREEN if recipe in craftable_recipes else GRAY
            if i == selected_recipe:
                # Highlight selected recipe
                highlight_rect = pygame.Rect(menu_rect.x + 10, y_offset - 2, 
                                           menu_rect.width - 20, 25)
                pygame.draw.rect(screen, DARK_BLUE, highlight_rect)
            
            # Recipe name
            item_name = self.get_item_name(recipe.result_item)
            recipe_text = small_font.render(f"{item_name} x{recipe.result_amount}", True, color)
            screen.blit(recipe_text, (menu_rect.x + 20, y_offset))
            
            # Ingredients
            ingredients_text = ""
            for item_type, amount in recipe.ingredients.items():
                ingredient_name = self.get_item_name(item_type)
                have_amount = inventory.get(item_type, 0)
                ingredients_text += f"{ingredient_name}: {have_amount}/{amount}  "
            
            ingredients_surface = small_font.render(ingredients_text, True, color)
            screen.blit(ingredients_surface, (menu_rect.x + 30, y_offset + 15))
            
            y_offset += 40
        
        # Instructions
        instructions = [
            "UP/DOWN: Select Recipe",
            "ENTER: Craft Item",
            "C: Close Crafting Menu"
        ]
        
        for i, instruction in enumerate(instructions):
            text = small_font.render(instruction, True, WHITE)
            screen.blit(text, (menu_rect.x + 20, menu_rect.bottom - 80 + i * 20))
        
        return len(all_recipes)
