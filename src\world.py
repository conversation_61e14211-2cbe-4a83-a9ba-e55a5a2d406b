"""
World class - handles world generation, block management, and world state
"""

import pygame
import random
import math
from src.constants import *

class World:
    def __init__(self, width=100, height=100):
        """Initialize the world"""
        self.width = width
        self.height = height
        self.camera_x = 0
        self.camera_y = 0
        
        # Create world data (2D array of block types)
        self.blocks = [[BLOCK_AIR for _ in range(width)] for _ in range(height)]
        
        # Block colors for rendering
        self.block_colors = {
            BLOCK_AIR: None,
            BLOCK_GRASS: GREEN,
            BLOCK_DIRT: BROWN,
            BLOCK_STONE: GRAY,
            BLOCK_WOOD: (101, 67, 33),
            BLOCK_LEAVES: (34, 139, 34),
            BLOCK_WATER: BLUE,
            BLOCK_SAND: YELLOW
        }
        
        # Generate the world
        self.generate_world()
        
    def generate_world(self):
        """Generate a basic world with terrain"""
        # Generate terrain using simple sine waves for hills
        base_height = self.height // 2

        for x in range(self.width):
            # Generate height map using sine waves for natural-looking terrain
            height_variation = math.sin(x * 0.05) * 15 + math.sin(x * 0.02) * 8
            terrain_height = int(base_height + height_variation)

            # Clamp height to world bounds
            terrain_height = max(10, min(self.height - 10, terrain_height))

            # Fill from bottom to terrain height
            for y in range(terrain_height, self.height):
                if y == terrain_height:
                    self.blocks[y][x] = BLOCK_GRASS
                elif y < terrain_height + 5:
                    self.blocks[y][x] = BLOCK_DIRT
                else:
                    self.blocks[y][x] = BLOCK_STONE
        
        # Add some trees
        self.generate_trees()
        
        # Add some water bodies
        self.generate_water()
        
    def generate_trees(self):
        """Generate trees randomly across the world"""
        for _ in range(self.width // 10):  # Roughly 1 tree per 10 blocks
            x = random.randint(5, self.width - 5)
            
            # Find ground level
            ground_y = None
            for y in range(self.height):
                if self.blocks[y][x] == BLOCK_GRASS:
                    ground_y = y
                    break
                    
            if ground_y is not None and ground_y > 5:
                # Create tree trunk
                trunk_height = random.randint(3, 6)
                for i in range(trunk_height):
                    if ground_y - i - 1 >= 0:
                        self.blocks[ground_y - i - 1][x] = BLOCK_WOOD
                
                # Create leaves
                leaf_y = ground_y - trunk_height - 1
                if leaf_y >= 2:
                    for dx in range(-2, 3):
                        for dy in range(-2, 3):
                            if 0 <= x + dx < self.width and 0 <= leaf_y + dy < self.height:
                                if abs(dx) + abs(dy) <= 2:  # Diamond shape
                                    self.blocks[leaf_y + dy][x + dx] = BLOCK_LEAVES
    
    def generate_water(self):
        """Generate small water bodies"""
        for _ in range(self.width // 20):  # Small water bodies
            center_x = random.randint(10, self.width - 10)
            
            # Find ground level
            ground_y = None
            for y in range(self.height):
                if self.blocks[y][center_x] != BLOCK_AIR:
                    ground_y = y
                    break
                    
            if ground_y is not None:
                # Create small pond
                pond_size = random.randint(2, 4)
                for dx in range(-pond_size, pond_size + 1):
                    for dy in range(pond_size):
                        px, py = center_x + dx, ground_y + dy
                        if 0 <= px < self.width and 0 <= py < self.height:
                            if abs(dx) + dy <= pond_size:
                                self.blocks[py][px] = BLOCK_WATER
    
    def get_block(self, x, y):
        """Get block type at coordinates"""
        if 0 <= x < self.width and 0 <= y < self.height:
            return self.blocks[y][x]
        return BLOCK_AIR
    
    def set_block(self, x, y, block_type):
        """Set block type at coordinates"""
        if 0 <= x < self.width and 0 <= y < self.height:
            self.blocks[y][x] = block_type
    
    def update(self, dt, player):
        """Update world state"""
        # Update camera to follow player
        self.camera_x = player.x - SCREEN_WIDTH // 2
        self.camera_y = player.y - SCREEN_HEIGHT // 2
        
        # Clamp camera to world bounds
        self.camera_x = max(0, min(self.camera_x, self.width * TILE_SIZE - SCREEN_WIDTH))
        self.camera_y = max(0, min(self.camera_y, self.height * TILE_SIZE - SCREEN_HEIGHT))
    
    def render(self, screen, camera):
        """Render the visible portion of the world"""
        # Calculate visible tile range
        start_x = max(0, int(camera.x // TILE_SIZE))
        end_x = min(self.width, int((camera.x + SCREEN_WIDTH) // TILE_SIZE) + 1)
        start_y = max(0, int(camera.y // TILE_SIZE))
        end_y = min(self.height, int((camera.y + SCREEN_HEIGHT) // TILE_SIZE) + 1)
        
        # Render visible tiles
        for y in range(start_y, end_y):
            for x in range(start_x, end_x):
                block_type = self.blocks[y][x]
                if block_type != BLOCK_AIR and block_type in self.block_colors:
                    color = self.block_colors[block_type]
                    if color:
                        screen_x = x * TILE_SIZE - camera.x
                        screen_y = y * TILE_SIZE - camera.y
                        pygame.draw.rect(screen, color, 
                                       (screen_x, screen_y, TILE_SIZE, TILE_SIZE))
                        
                        # Add border for better visibility
                        pygame.draw.rect(screen, DARK_GRAY, 
                                       (screen_x, screen_y, TILE_SIZE, TILE_SIZE), 1)
