"""
Professional Menu System - Dead Cells/Graveyard Keeper Style
"""

import pygame
import math
from src.constants import *

class MenuButton:
    def __init__(self, x, y, width, height, text, font, action=None):
        """Initialize menu button"""
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.font = font
        self.action = action
        self.hovered = False
        self.pressed = False
        self.hover_scale = 1.0
        self.glow_intensity = 0
    
    def update(self, dt, mouse_pos, mouse_clicked):
        """Update button state"""
        self.hovered = self.rect.collidepoint(mouse_pos)
        
        # Smooth hover animation
        target_scale = 1.05 if self.hovered else 1.0
        self.hover_scale += (target_scale - self.hover_scale) * 8 * dt
        
        # Glow effect
        target_glow = 100 if self.hovered else 0
        self.glow_intensity += (target_glow - self.glow_intensity) * 6 * dt
        
        # Check for click
        if self.hovered and mouse_clicked and self.action:
            self.action()
            return True
        
        return False
    
    def render(self, screen, sprite_manager):
        """Render button with professional styling"""
        # Calculate scaled rect
        center_x, center_y = self.rect.center
        scaled_width = int(self.rect.width * self.hover_scale)
        scaled_height = int(self.rect.height * self.hover_scale)
        scaled_rect = pygame.Rect(0, 0, scaled_width, scaled_height)
        scaled_rect.center = (center_x, center_y)
        
        # Glow effect
        if self.glow_intensity > 0:
            glow_surf = pygame.Surface((scaled_width + 20, scaled_height + 20), pygame.SRCALPHA)
            glow_color = (100, 150, 255, int(self.glow_intensity))
            pygame.draw.rect(glow_surf, glow_color, (0, 0, scaled_width + 20, scaled_height + 20), 10)
            screen.blit(glow_surf, (scaled_rect.x - 10, scaled_rect.y - 10))
        
        # Button background
        if self.hovered:
            button_color = (90, 150, 200)
            border_color = (120, 180, 230)
        else:
            button_color = (70, 130, 180)
            border_color = (100, 160, 210)
        
        pygame.draw.rect(screen, button_color, scaled_rect)
        pygame.draw.rect(screen, border_color, scaled_rect, 3)
        
        # Button highlight
        highlight_rect = pygame.Rect(scaled_rect.x + 3, scaled_rect.y + 3, 
                                   scaled_rect.width - 6, scaled_rect.height // 3)
        pygame.draw.rect(screen, (130, 190, 240, 100), highlight_rect)
        
        # Button text
        text_color = WHITE if not self.hovered else (255, 255, 200)
        text_surface = self.font.render(self.text, True, text_color)
        text_rect = text_surface.get_rect(center=scaled_rect.center)
        screen.blit(text_surface, text_rect)


class MainMenu:
    def __init__(self, screen_width, screen_height):
        """Initialize main menu"""
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # Fonts
        self.title_font = pygame.font.Font(None, 72)
        self.button_font = pygame.font.Font(None, 36)
        self.subtitle_font = pygame.font.Font(None, 24)
        
        # Animation
        self.title_glow = 0
        self.title_glow_direction = 1
        self.background_scroll = 0
        
        # Menu state
        self.selected_option = 0
        self.transition_alpha = 0
        self.transitioning = False
        
        # Buttons
        self.buttons = []
        self.create_buttons()
        
        # Background particles
        self.particles = []
        self.init_background_particles()
    
    def create_buttons(self):
        """Create menu buttons"""
        button_width = 200
        button_height = 50
        button_spacing = 70
        start_y = self.screen_height // 2 + 50
        
        buttons_data = [
            ("NEW GAME", self.start_new_game),
            ("CONTINUE", self.continue_game),
            ("OPTIONS", self.show_options),
            ("QUIT", self.quit_game)
        ]
        
        for i, (text, action) in enumerate(buttons_data):
            x = self.screen_width // 2 - button_width // 2
            y = start_y + i * button_spacing
            button = MenuButton(x, y, button_width, button_height, text, self.button_font, action)
            self.buttons.append(button)
    
    def init_background_particles(self):
        """Initialize background particles"""
        for i in range(50):
            particle = {
                'x': pygame.math.Vector2(
                    random.randint(0, self.screen_width),
                    random.randint(0, self.screen_height)
                ),
                'velocity': pygame.math.Vector2(
                    random.uniform(-20, 20),
                    random.uniform(-30, -10)
                ),
                'size': random.randint(1, 3),
                'alpha': random.randint(50, 150),
                'color': random.choice([(100, 100, 100), (80, 80, 80), (120, 120, 120)])
            }
            self.particles.append(particle)
    
    def start_new_game(self):
        """Start new game"""
        self.transitioning = True
        return "new_game"
    
    def continue_game(self):
        """Continue existing game"""
        return "continue"
    
    def show_options(self):
        """Show options menu"""
        return "options"
    
    def quit_game(self):
        """Quit game"""
        return "quit"
    
    def update(self, dt, mouse_pos, mouse_clicked, keys):
        """Update menu"""
        # Title glow animation
        self.title_glow += self.title_glow_direction * 100 * dt
        if self.title_glow >= 100:
            self.title_glow = 100
            self.title_glow_direction = -1
        elif self.title_glow <= 0:
            self.title_glow = 0
            self.title_glow_direction = 1
        
        # Background scroll
        self.background_scroll += 20 * dt
        if self.background_scroll >= self.screen_height:
            self.background_scroll = 0
        
        # Update particles
        for particle in self.particles:
            particle['x'] += particle['velocity'] * dt
            
            # Wrap around screen
            if particle['x'].x < 0:
                particle['x'].x = self.screen_width
            elif particle['x'].x > self.screen_width:
                particle['x'].x = 0
            
            if particle['x'].y < 0:
                particle['x'].y = self.screen_height
            elif particle['x'].y > self.screen_height:
                particle['x'].y = 0
        
        # Update buttons
        result = None
        for button in self.buttons:
            if button.update(dt, mouse_pos, mouse_clicked):
                result = button.action()
        
        # Keyboard navigation
        if keys[pygame.K_UP]:
            self.selected_option = (self.selected_option - 1) % len(self.buttons)
        elif keys[pygame.K_DOWN]:
            self.selected_option = (self.selected_option + 1) % len(self.buttons)
        elif keys[pygame.K_RETURN]:
            result = self.buttons[self.selected_option].action()
        
        # Transition effect
        if self.transitioning:
            self.transition_alpha += 300 * dt
            if self.transition_alpha >= 255:
                return "new_game"
        
        return result
    
    def render(self, screen, sprite_manager):
        """Render main menu"""
        # Background gradient
        for y in range(self.screen_height):
            progress = y / self.screen_height
            color_r = int(20 + progress * 30)
            color_g = int(25 + progress * 35)
            color_b = int(40 + progress * 50)
            pygame.draw.line(screen, (color_r, color_g, color_b), (0, y), (self.screen_width, y))
        
        # Background particles
        for particle in self.particles:
            pygame.draw.circle(screen, (*particle['color'], particle['alpha']), 
                             (int(particle['x'].x), int(particle['x'].y)), particle['size'])
        
        # Title with glow effect
        title_text = "ZOMBIE CRAFT"
        subtitle_text = "Survive the Apocalypse"
        
        # Title glow
        glow_color = (100 + int(self.title_glow), 50 + int(self.title_glow // 2), 50)
        for offset in range(5, 0, -1):
            glow_surface = self.title_font.render(title_text, True, glow_color)
            glow_surface.set_alpha(50)
            glow_rect = glow_surface.get_rect(center=(self.screen_width // 2 + offset, 150 + offset))
            screen.blit(glow_surface, glow_rect)
        
        # Main title
        title_surface = self.title_font.render(title_text, True, (255, 200 + int(self.title_glow // 2), 200))
        title_rect = title_surface.get_rect(center=(self.screen_width // 2, 150))
        screen.blit(title_surface, title_rect)
        
        # Subtitle
        subtitle_surface = self.subtitle_font.render(subtitle_text, True, (200, 200, 200))
        subtitle_rect = subtitle_surface.get_rect(center=(self.screen_width // 2, 190))
        screen.blit(subtitle_surface, subtitle_rect)
        
        # Render buttons
        for i, button in enumerate(self.buttons):
            if i == self.selected_option:
                button.hovered = True
            button.render(screen, sprite_manager)
        
        # Version info
        version_text = "v1.0 - Professional Edition"
        version_surface = self.subtitle_font.render(version_text, True, (150, 150, 150))
        screen.blit(version_surface, (10, self.screen_height - 30))
        
        # Controls hint
        controls_text = "Use ARROW KEYS and ENTER to navigate"
        controls_surface = self.subtitle_font.render(controls_text, True, (150, 150, 150))
        controls_rect = controls_surface.get_rect(center=(self.screen_width // 2, self.screen_height - 30))
        screen.blit(controls_surface, controls_rect)
        
        # Transition overlay
        if self.transitioning and self.transition_alpha > 0:
            transition_surface = pygame.Surface((self.screen_width, self.screen_height))
            transition_surface.fill((0, 0, 0))
            transition_surface.set_alpha(min(255, int(self.transition_alpha)))
            screen.blit(transition_surface, (0, 0))
