"""
Zombie AI and behavior system
"""

import pygame
import math
import random
from src.constants import *

class Zombie:
    def __init__(self, x, y):
        """Initialize zombie"""
        self.x = x
        self.y = y
        self.width = TILE_SIZE - 4
        self.height = TILE_SIZE - 4
        
        # Movement
        self.vel_x = 0
        self.vel_y = 0
        self.speed = ZOMBIE_SPEED
        self.chase_speed = ZOMBIE_CHASE_SPEED
        
        # AI states
        self.state = "wandering"  # wandering, chasing, attacking
        self.target = None
        self.last_seen_player_pos = None
        
        # Stats
        self.health = 50
        self.max_health = 50
        self.damage = 20
        self.attack_cooldown = 0
        self.attack_range = TILE_SIZE * 1.5
        self.detection_range = TILE_SIZE * 8
        
        # Wandering behavior
        self.wander_timer = 0
        self.wander_direction = random.uniform(0, 2 * math.pi)
        self.wander_change_time = random.uniform(2, 5)
        
        # Animation
        self.animation_timer = 0
        self.sprite_offset = 0
        
        # Pathfinding
        self.stuck_timer = 0
        self.last_position = (x, y)
        
    def update(self, dt, player, world, other_zombies):
        """Update zombie AI and behavior"""
        self.animation_timer += dt
        self.attack_cooldown = max(0, self.attack_cooldown - dt)
        
        # Check if stuck
        if abs(self.x - self.last_position[0]) < 5 and abs(self.y - self.last_position[1]) < 5:
            self.stuck_timer += dt
        else:
            self.stuck_timer = 0
            self.last_position = (self.x, self.y)
        
        # Distance to player
        distance_to_player = math.sqrt((self.x - player.x)**2 + (self.y - player.y)**2)
        
        # State machine
        if distance_to_player <= self.attack_range and self.state != "attacking":
            self.state = "attacking"
            self.target = player
        elif distance_to_player <= self.detection_range:
            self.state = "chasing"
            self.target = player
            self.last_seen_player_pos = (player.x, player.y)
        elif self.state == "chasing" and self.last_seen_player_pos:
            # Continue to last known position
            dist_to_last_pos = math.sqrt((self.x - self.last_seen_player_pos[0])**2 + 
                                       (self.y - self.last_seen_player_pos[1])**2)
            if dist_to_last_pos < TILE_SIZE:
                self.state = "wandering"
                self.last_seen_player_pos = None
        else:
            self.state = "wandering"
            self.target = None
        
        # Behavior based on state
        if self.state == "attacking":
            self.attack_behavior(dt, player)
        elif self.state == "chasing":
            self.chase_behavior(dt, player, world)
        else:
            self.wander_behavior(dt, world)
        
        # Apply movement with collision
        self.move(dt, world)
        
        # Avoid other zombies
        self.avoid_zombies(other_zombies)
    
    def attack_behavior(self, dt, player):
        """Attack the player"""
        if self.attack_cooldown <= 0:
            # Deal damage to player
            player.take_damage(self.damage)
            self.attack_cooldown = 1.5  # 1.5 second cooldown
            
        # Stop moving when attacking
        self.vel_x = 0
        self.vel_y = 0
    
    def chase_behavior(self, dt, player, world):
        """Chase the player or last known position"""
        target_x, target_y = (player.x, player.y) if self.target else self.last_seen_player_pos
        
        # Calculate direction to target
        dx = target_x - self.x
        dy = target_y - self.y
        distance = math.sqrt(dx**2 + dy**2)
        
        if distance > 0:
            # Normalize direction
            dx /= distance
            dy /= distance
            
            # Set velocity
            self.vel_x = dx * self.chase_speed
            self.vel_y = dy * self.chase_speed
            
            # If stuck, try different direction
            if self.stuck_timer > 2:
                angle = random.uniform(0, 2 * math.pi)
                self.vel_x = math.cos(angle) * self.chase_speed
                self.vel_y = math.sin(angle) * self.chase_speed
                self.stuck_timer = 0
    
    def wander_behavior(self, dt, world):
        """Random wandering behavior"""
        self.wander_timer += dt
        
        if self.wander_timer >= self.wander_change_time or self.stuck_timer > 1:
            # Change direction
            self.wander_direction = random.uniform(0, 2 * math.pi)
            self.wander_change_time = random.uniform(2, 5)
            self.wander_timer = 0
            self.stuck_timer = 0
        
        # Move in wander direction
        self.vel_x = math.cos(self.wander_direction) * self.speed
        self.vel_y = math.sin(self.wander_direction) * self.speed
    
    def move(self, dt, world):
        """Move zombie with collision detection"""
        # Calculate new position
        new_x = self.x + self.vel_x * dt
        new_y = self.y + self.vel_y * dt
        
        # Simple collision with world bounds
        world_width = world.width * TILE_SIZE
        world_height = world.height * TILE_SIZE
        
        if new_x >= 0 and new_x <= world_width - self.width:
            self.x = new_x
        else:
            self.vel_x = -self.vel_x  # Bounce off walls
            
        if new_y >= 0 and new_y <= world_height - self.height:
            self.y = new_y
        else:
            self.vel_y = -self.vel_y  # Bounce off walls
        
        # Simple block collision (avoid water and solid blocks)
        tile_x = int(self.x // TILE_SIZE)
        tile_y = int(self.y // TILE_SIZE)
        
        block_type = world.get_block(tile_x, tile_y)
        if block_type == BLOCK_WATER:
            # Zombies avoid water
            self.vel_x = -self.vel_x * 2
            self.vel_y = -self.vel_y * 2
    
    def avoid_zombies(self, other_zombies):
        """Avoid overlapping with other zombies"""
        for other in other_zombies:
            if other == self:
                continue
                
            dx = self.x - other.x
            dy = self.y - other.y
            distance = math.sqrt(dx**2 + dy**2)
            
            if distance < TILE_SIZE and distance > 0:
                # Push away from other zombie
                push_force = (TILE_SIZE - distance) * 0.5
                dx /= distance
                dy /= distance
                
                self.x += dx * push_force
                self.y += dy * push_force
    
    def take_damage(self, damage):
        """Take damage"""
        self.health -= damage
        if self.health <= 0:
            return True  # Zombie is dead
        return False
    
    def render(self, screen, camera, sprite_manager):
        """Render the zombie"""
        screen_x = self.x - camera.x
        screen_y = self.y - camera.y
        
        # Get zombie sprite
        sprite = sprite_manager.get_sprite(ENTITY_ZOMBIE)
        if sprite:
            # Add animation offset for walking effect
            offset = int(math.sin(self.animation_timer * 8) * 2) if self.vel_x != 0 or self.vel_y != 0 else 0
            screen.blit(sprite, (screen_x, screen_y + offset))
        
        # Health bar
        if self.health < self.max_health:
            bar_width = self.width
            bar_height = 4
            bar_x = screen_x
            bar_y = screen_y - 8
            
            # Background
            pygame.draw.rect(screen, DARK_RED, (bar_x, bar_y, bar_width, bar_height))
            # Health
            health_width = int((self.health / self.max_health) * bar_width)
            pygame.draw.rect(screen, RED, (bar_x, bar_y, health_width, bar_height))
        
        # State indicator (for debugging)
        if self.state == "chasing":
            pygame.draw.circle(screen, YELLOW, (int(screen_x + self.width//2), int(screen_y - 15)), 3)
        elif self.state == "attacking":
            pygame.draw.circle(screen, RED, (int(screen_x + self.width//2), int(screen_y - 15)), 3)


class ZombieManager:
    def __init__(self):
        """Initialize zombie manager"""
        self.zombies = []
        self.spawn_timer = 0
        self.spawn_interval = 10  # Spawn every 10 seconds
        self.max_zombies = 20
        
    def update(self, dt, player, world):
        """Update all zombies"""
        # Spawn new zombies
        self.spawn_timer += dt
        if self.spawn_timer >= self.spawn_interval and len(self.zombies) < self.max_zombies:
            self.spawn_zombie(player, world)
            self.spawn_timer = 0
        
        # Update existing zombies
        zombies_to_remove = []
        for zombie in self.zombies:
            zombie.update(dt, player, world, self.zombies)
            
            # Remove dead zombies
            if zombie.health <= 0:
                zombies_to_remove.append(zombie)
        
        # Remove dead zombies
        for zombie in zombies_to_remove:
            self.zombies.remove(zombie)
    
    def spawn_zombie(self, player, world):
        """Spawn a new zombie away from player"""
        attempts = 0
        while attempts < 10:
            # Try to spawn away from player
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(TILE_SIZE * 15, TILE_SIZE * 25)
            
            spawn_x = player.x + math.cos(angle) * distance
            spawn_y = player.y + math.sin(angle) * distance
            
            # Clamp to world bounds
            spawn_x = max(TILE_SIZE, min(spawn_x, world.width * TILE_SIZE - TILE_SIZE))
            spawn_y = max(TILE_SIZE, min(spawn_y, world.height * TILE_SIZE - TILE_SIZE))
            
            # Check if spawn location is valid (not in water)
            tile_x = int(spawn_x // TILE_SIZE)
            tile_y = int(spawn_y // TILE_SIZE)
            
            if world.get_block(tile_x, tile_y) != BLOCK_WATER:
                zombie = Zombie(spawn_x, spawn_y)
                self.zombies.append(zombie)
                break
                
            attempts += 1
    
    def render(self, screen, camera, sprite_manager):
        """Render all zombies"""
        for zombie in self.zombies:
            zombie.render(screen, camera, sprite_manager)
    
    def get_zombie_count(self):
        """Get current zombie count"""
        return len(self.zombies)
