"""
Sprite generation and management system
"""

import pygame
from src.constants import *

class SpriteManager:
    def __init__(self):
        """Initialize sprite manager"""
        self.sprites = {}
        self.generate_all_sprites()
    
    def generate_all_sprites(self):
        """Generate all game sprites"""
        # Block sprites
        self.generate_block_sprites()
        
        # Entity sprites
        self.generate_entity_sprites()
        
        # Item sprites
        self.generate_item_sprites()
        
        # UI sprites
        self.generate_ui_sprites()
    
    def generate_block_sprites(self):
        """Generate block sprites"""
        # Grass block
        grass = pygame.Surface((TILE_SIZE, TILE_SIZE))
        grass.fill(DARK_GREEN)
        pygame.draw.rect(grass, GREEN, (0, 0, TILE_SIZE, 8))
        self.sprites[BLOCK_GRASS] = grass
        
        # Dirt block
        dirt = pygame.Surface((TILE_SIZE, TILE_SIZE))
        dirt.fill(BROWN)
        for i in range(5):
            x, y = i * 6, i * 5
            pygame.draw.circle(dirt, DARK_BROWN, (x % TILE_SIZE, y % TILE_SIZE), 2)
        self.sprites[BLOCK_DIRT] = dirt
        
        # Stone block
        stone = pygame.Surface((TILE_SIZE, TILE_SIZE))
        stone.fill(GRAY)
        for i in range(8):
            x, y = (i * 7) % TILE_SIZE, (i * 11) % TILE_SIZE
            pygame.draw.circle(stone, DARK_GRAY, (x, y), 1)
        self.sprites[BLOCK_STONE] = stone
        
        # Wood block
        wood = pygame.Surface((TILE_SIZE, TILE_SIZE))
        wood.fill(DARK_BROWN)
        for i in range(0, TILE_SIZE, 4):
            pygame.draw.line(wood, BROWN, (0, i), (TILE_SIZE, i))
        self.sprites[BLOCK_WOOD] = wood
        
        # Leaves block
        leaves = pygame.Surface((TILE_SIZE, TILE_SIZE))
        leaves.fill(DARK_GREEN)
        leaves.set_alpha(200)
        for i in range(10):
            x, y = (i * 3) % TILE_SIZE, (i * 7) % TILE_SIZE
            pygame.draw.circle(leaves, GREEN, (x, y), 2)
        self.sprites[BLOCK_LEAVES] = leaves
        
        # Water block
        water = pygame.Surface((TILE_SIZE, TILE_SIZE))
        water.fill(BLUE)
        water.set_alpha(180)
        for i in range(3):
            y = i * 10 + 5
            pygame.draw.line(water, DARK_BLUE, (0, y), (TILE_SIZE, y), 2)
        self.sprites[BLOCK_WATER] = water
        
        # Sand block
        sand = pygame.Surface((TILE_SIZE, TILE_SIZE))
        sand.fill(YELLOW)
        for i in range(15):
            x, y = (i * 5) % TILE_SIZE, (i * 3) % TILE_SIZE
            pygame.draw.circle(sand, ORANGE, (x, y), 1)
        self.sprites[BLOCK_SAND] = sand
        
        # Cobblestone
        cobble = pygame.Surface((TILE_SIZE, TILE_SIZE))
        cobble.fill(LIGHT_GRAY)
        for i in range(4):
            for j in range(4):
                x, y = i * 8, j * 8
                pygame.draw.rect(cobble, GRAY, (x, y, 7, 7))
                pygame.draw.rect(cobble, DARK_GRAY, (x, y, 7, 7), 1)
        self.sprites[BLOCK_COBBLESTONE] = cobble
        
        # Planks
        planks = pygame.Surface((TILE_SIZE, TILE_SIZE))
        planks.fill(BROWN)
        for i in range(0, TILE_SIZE, 8):
            pygame.draw.line(planks, DARK_BROWN, (0, i), (TILE_SIZE, i))
            pygame.draw.line(planks, DARK_BROWN, (i, 0), (i, TILE_SIZE))
        self.sprites[BLOCK_PLANKS] = planks
        
        # Coal ore
        coal_ore = pygame.Surface((TILE_SIZE, TILE_SIZE))
        coal_ore.fill(GRAY)
        for i in range(6):
            x, y = (i * 7) % TILE_SIZE, (i * 11) % TILE_SIZE
            pygame.draw.circle(coal_ore, BLACK, (x, y), 3)
        self.sprites[BLOCK_COAL_ORE] = coal_ore
        
        # Iron ore
        iron_ore = pygame.Surface((TILE_SIZE, TILE_SIZE))
        iron_ore.fill(GRAY)
        for i in range(4):
            x, y = (i * 9) % TILE_SIZE, (i * 13) % TILE_SIZE
            pygame.draw.circle(iron_ore, LIGHT_GRAY, (x, y), 4)
        self.sprites[BLOCK_IRON_ORE] = iron_ore
    
    def generate_entity_sprites(self):
        """Generate entity sprites"""
        # Player sprite
        player = pygame.Surface((TILE_SIZE - 4, TILE_SIZE - 4))
        player.fill(BLUE)
        # Add simple face
        pygame.draw.circle(player, WHITE, (8, 8), 2)
        pygame.draw.circle(player, WHITE, (20, 8), 2)
        pygame.draw.arc(player, WHITE, (6, 15, 16, 8), 0, 3.14)
        self.sprites['player'] = player
        
        # Zombie sprite
        zombie = pygame.Surface((TILE_SIZE - 4, TILE_SIZE - 4))
        zombie.fill(ZOMBIE_GREEN)
        # Add zombie face
        pygame.draw.circle(zombie, RED, (8, 8), 2)
        pygame.draw.circle(zombie, RED, (20, 8), 2)
        pygame.draw.line(zombie, DARK_RED, (6, 20), (22, 20), 2)
        self.sprites[ENTITY_ZOMBIE] = zombie
        
        # Survivor sprite
        survivor = pygame.Surface((TILE_SIZE - 4, TILE_SIZE - 4))
        survivor.fill(ORANGE)
        pygame.draw.circle(survivor, WHITE, (8, 8), 2)
        pygame.draw.circle(survivor, WHITE, (20, 8), 2)
        pygame.draw.arc(survivor, WHITE, (6, 15, 16, 8), 0, 3.14)
        self.sprites[ENTITY_SURVIVOR] = survivor
    
    def generate_item_sprites(self):
        """Generate item sprites"""
        # Wood item
        wood_item = pygame.Surface((16, 16))
        wood_item.fill(BROWN)
        self.sprites[ITEM_WOOD] = wood_item
        
        # Stone item
        stone_item = pygame.Surface((16, 16))
        stone_item.fill(GRAY)
        self.sprites[ITEM_STONE] = stone_item
        
        # Stick item
        stick_item = pygame.Surface((16, 16))
        stick_item.fill(DARK_BROWN)
        pygame.draw.line(stick_item, BROWN, (2, 2), (14, 14), 2)
        self.sprites[ITEM_STICK] = stick_item
        
        # Coal item
        coal_item = pygame.Surface((16, 16))
        coal_item.fill(BLACK)
        self.sprites[ITEM_COAL] = coal_item
        
        # Iron item
        iron_item = pygame.Surface((16, 16))
        iron_item.fill(LIGHT_GRAY)
        self.sprites[ITEM_IRON] = iron_item
        
        # Food item
        food_item = pygame.Surface((16, 16))
        food_item.fill(RED)
        pygame.draw.circle(food_item, DARK_RED, (8, 8), 6)
        self.sprites[ITEM_FOOD] = food_item
    
    def generate_ui_sprites(self):
        """Generate UI sprites"""
        # Inventory slot
        slot = pygame.Surface((40, 40))
        slot.fill(DARK_GRAY)
        pygame.draw.rect(slot, GRAY, (0, 0, 40, 40), 2)
        self.sprites['inventory_slot'] = slot
        
        # Selected slot
        selected_slot = pygame.Surface((40, 40))
        selected_slot.fill(DARK_GRAY)
        pygame.draw.rect(selected_slot, YELLOW, (0, 0, 40, 40), 3)
        self.sprites['selected_slot'] = selected_slot
    
    def get_sprite(self, sprite_id):
        """Get sprite by ID"""
        return self.sprites.get(sprite_id, None)
    
    def get_block_sprite(self, block_type):
        """Get block sprite"""
        return self.sprites.get(block_type, None)
