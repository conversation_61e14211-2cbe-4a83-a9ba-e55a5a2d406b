"""
Professional Pixel Art Sprite System - Dead Cells/Graveyard Keeper Style
"""

import pygame
import math
from src.constants import *

class SpriteManager:
    def __init__(self):
        """Initialize professional sprite manager"""
        self.sprites = {}
        self.animations = {}
        self.generate_all_sprites()

    def generate_all_sprites(self):
        """Generate all professional game sprites"""
        print("Generating professional pixel art sprites...")

        # Block sprites with detailed pixel art
        self.generate_professional_block_sprites()

        # Character sprites with animations
        self.generate_professional_character_sprites()

        # Item sprites with detailed art
        self.generate_professional_item_sprites()

        # UI sprites with modern design
        self.generate_professional_ui_sprites()

        # Particle and effect sprites
        self.generate_effect_sprites()

        print("Professional sprites generated!")
    
    def generate_professional_block_sprites(self):
        """Generate professional pixel art block sprites"""

        # Professional Grass Block - Dead Cells style
        grass = pygame.Surface((TILE_SIZE, TILE_SIZE))
        grass.fill((45, 85, 35))  # Dark base

        # Grass top layer with pixel detail
        for x in range(TILE_SIZE):
            for y in range(6):
                if y < 3:
                    # Bright grass top
                    color = (85, 170, 65) if (x + y) % 3 != 0 else (105, 190, 85)
                else:
                    # Grass-dirt transition
                    color = (65, 120, 45) if (x + y) % 2 == 0 else (55, 100, 40)
                grass.set_at((x, y), color)

        # Add grass blade details
        for i in range(8):
            x = (i * 4 + 2) % TILE_SIZE
            grass.set_at((x, 0), (120, 220, 100))
            grass.set_at((x, 1), (100, 200, 80))

        self.sprites[BLOCK_GRASS] = grass

        # Professional Dirt Block
        dirt = pygame.Surface((TILE_SIZE, TILE_SIZE))
        base_color = (101, 67, 33)
        dirt.fill(base_color)

        # Add dirt texture with pixel art detail
        for x in range(TILE_SIZE):
            for y in range(TILE_SIZE):
                noise = (x * 7 + y * 11) % 16
                if noise < 4:
                    color = (121, 87, 53)  # Lighter dirt
                elif noise < 8:
                    color = (81, 47, 23)   # Darker dirt
                else:
                    color = base_color
                dirt.set_at((x, y), color)

        # Add small rocks/pebbles
        for i in range(6):
            x, y = (i * 7) % (TILE_SIZE-2), (i * 11) % (TILE_SIZE-2)
            pygame.draw.circle(dirt, (140, 100, 60), (x+1, y+1), 1)

        self.sprites[BLOCK_DIRT] = dirt

        # Professional Stone Block
        stone = pygame.Surface((TILE_SIZE, TILE_SIZE))
        base_color = (128, 128, 128)
        stone.fill(base_color)

        # Create stone texture
        for x in range(TILE_SIZE):
            for y in range(TILE_SIZE):
                noise = (x * 3 + y * 5) % 12
                if noise < 3:
                    color = (148, 148, 148)  # Lighter stone
                elif noise < 6:
                    color = (108, 108, 108)  # Darker stone
                else:
                    color = base_color
                stone.set_at((x, y), color)

        # Add stone cracks and details
        for i in range(4):
            x1, y1 = (i * 8) % TILE_SIZE, (i * 6) % TILE_SIZE
            x2, y2 = x1 + 4, y1 + 2
            pygame.draw.line(stone, (88, 88, 88), (x1, y1), (x2, y2))

        self.sprites[BLOCK_STONE] = stone
        
        # Professional Wood Block
        wood = pygame.Surface((TILE_SIZE, TILE_SIZE))
        base_color = (101, 67, 33)
        wood.fill(base_color)

        # Wood grain pattern
        for y in range(TILE_SIZE):
            for x in range(TILE_SIZE):
                # Vertical wood grain
                grain = (x + y * 2) % 8
                if grain < 2:
                    color = (121, 87, 53)  # Light grain
                elif grain < 4:
                    color = (81, 47, 23)   # Dark grain
                else:
                    color = base_color
                wood.set_at((x, y), color)

        # Add wood rings
        center_x, center_y = TILE_SIZE // 2, TILE_SIZE // 2
        for radius in [6, 12, 18]:
            for angle in range(0, 360, 10):
                x = center_x + int(radius * math.cos(math.radians(angle)))
                y = center_y + int(radius * math.sin(math.radians(angle)))
                if 0 <= x < TILE_SIZE and 0 <= y < TILE_SIZE:
                    wood.set_at((x, y), (61, 27, 13))

        self.sprites[BLOCK_WOOD] = wood

        # Professional Leaves Block
        leaves = pygame.Surface((TILE_SIZE, TILE_SIZE))
        leaves.fill((34, 89, 34))
        leaves.set_alpha(220)

        # Detailed leaf pattern
        for x in range(TILE_SIZE):
            for y in range(TILE_SIZE):
                noise = (x * 5 + y * 7) % 20
                if noise < 6:
                    color = (54, 139, 54)  # Bright leaves
                elif noise < 12:
                    color = (24, 69, 24)   # Dark leaves
                else:
                    color = (34, 89, 34)   # Base
                leaves.set_at((x, y), color)

        # Add individual leaf details
        for i in range(12):
            x, y = (i * 7) % (TILE_SIZE-2), (i * 5) % (TILE_SIZE-2)
            pygame.draw.circle(leaves, (74, 159, 74), (x+1, y+1), 1)

        self.sprites[BLOCK_LEAVES] = leaves

        # Professional Water Block with animation potential
        water = pygame.Surface((TILE_SIZE, TILE_SIZE))
        base_blue = (30, 144, 255)
        water.fill(base_blue)
        water.set_alpha(200)

        # Water wave pattern
        for x in range(TILE_SIZE):
            for y in range(TILE_SIZE):
                wave = int(math.sin((x + y) * 0.3) * 20)
                color_mod = max(-30, min(30, wave))
                new_color = (
                    max(0, min(255, base_blue[0] + color_mod)),
                    max(0, min(255, base_blue[1] + color_mod)),
                    max(0, min(255, base_blue[2] + color_mod))
                )
                water.set_at((x, y), new_color)

        # Add water highlights
        for i in range(4):
            y = i * 8 + 2
            pygame.draw.line(water, (80, 184, 255), (0, y), (TILE_SIZE, y), 1)

        self.sprites[BLOCK_WATER] = water
        
        # Sand block
        sand = pygame.Surface((TILE_SIZE, TILE_SIZE))
        sand.fill(YELLOW)
        for i in range(15):
            x, y = (i * 5) % TILE_SIZE, (i * 3) % TILE_SIZE
            pygame.draw.circle(sand, ORANGE, (x, y), 1)
        self.sprites[BLOCK_SAND] = sand
        
        # Cobblestone
        cobble = pygame.Surface((TILE_SIZE, TILE_SIZE))
        cobble.fill(LIGHT_GRAY)
        for i in range(4):
            for j in range(4):
                x, y = i * 8, j * 8
                pygame.draw.rect(cobble, GRAY, (x, y, 7, 7))
                pygame.draw.rect(cobble, DARK_GRAY, (x, y, 7, 7), 1)
        self.sprites[BLOCK_COBBLESTONE] = cobble
        
        # Planks
        planks = pygame.Surface((TILE_SIZE, TILE_SIZE))
        planks.fill(BROWN)
        for i in range(0, TILE_SIZE, 8):
            pygame.draw.line(planks, DARK_BROWN, (0, i), (TILE_SIZE, i))
            pygame.draw.line(planks, DARK_BROWN, (i, 0), (i, TILE_SIZE))
        self.sprites[BLOCK_PLANKS] = planks
        
        # Coal ore
        coal_ore = pygame.Surface((TILE_SIZE, TILE_SIZE))
        coal_ore.fill(GRAY)
        for i in range(6):
            x, y = (i * 7) % TILE_SIZE, (i * 11) % TILE_SIZE
            pygame.draw.circle(coal_ore, BLACK, (x, y), 3)
        self.sprites[BLOCK_COAL_ORE] = coal_ore
        
        # Iron ore
        iron_ore = pygame.Surface((TILE_SIZE, TILE_SIZE))
        iron_ore.fill(GRAY)
        for i in range(4):
            x, y = (i * 9) % TILE_SIZE, (i * 13) % TILE_SIZE
            pygame.draw.circle(iron_ore, LIGHT_GRAY, (x, y), 4)
        self.sprites[BLOCK_IRON_ORE] = iron_ore
    
    def generate_professional_character_sprites(self):
        """Generate professional character sprites like Dead Cells"""

        # Professional Player Character - Human Survivor
        player_width, player_height = TILE_SIZE - 2, TILE_SIZE - 2

        # Create multiple animation frames for player
        player_idle = self.create_professional_player_sprite(player_width, player_height, "idle")
        player_walk1 = self.create_professional_player_sprite(player_width, player_height, "walk1")
        player_walk2 = self.create_professional_player_sprite(player_width, player_height, "walk2")

        self.sprites['player'] = player_idle
        self.sprites['player_walk1'] = player_walk1
        self.sprites['player_walk2'] = player_walk2

        # Professional Zombie - Detailed undead
        zombie_idle = self.create_professional_zombie_sprite(player_width, player_height, "idle")
        zombie_walk1 = self.create_professional_zombie_sprite(player_width, player_height, "walk1")
        zombie_walk2 = self.create_professional_zombie_sprite(player_width, player_height, "walk2")

        self.sprites[ENTITY_ZOMBIE] = zombie_idle
        self.sprites['zombie_walk1'] = zombie_walk1
        self.sprites['zombie_walk2'] = zombie_walk2

        # Professional Survivor NPC
        survivor = self.create_professional_survivor_sprite(player_width, player_height)
        self.sprites[ENTITY_SURVIVOR] = survivor

    def create_professional_player_sprite(self, width, height, animation_frame):
        """Create detailed player sprite like Dead Cells character"""
        sprite = pygame.Surface((width, height), pygame.SRCALPHA)

        # Body colors
        skin_color = (255, 220, 177)
        hair_color = (101, 67, 33)
        shirt_color = (70, 130, 180)
        pants_color = (47, 79, 79)
        boot_color = (25, 25, 25)

        # Head (detailed pixel art)
        head_y = 2
        for y in range(head_y, head_y + 8):
            for x in range(8, 22):
                if y < head_y + 2:  # Hair
                    sprite.set_at((x, y), hair_color)
                elif y < head_y + 6:  # Face
                    sprite.set_at((x, y), skin_color)

        # Eyes
        sprite.set_at((11, head_y + 3), (0, 0, 0))  # Left eye
        sprite.set_at((18, head_y + 3), (0, 0, 0))  # Right eye

        # Nose and mouth
        sprite.set_at((14, head_y + 4), (200, 180, 140))  # Nose
        sprite.set_at((14, head_y + 5), (180, 100, 100))  # Mouth

        # Body
        body_y = 10
        for y in range(body_y, body_y + 12):
            for x in range(10, 20):
                if y < body_y + 8:  # Shirt
                    sprite.set_at((x, y), shirt_color)
                else:  # Pants
                    sprite.set_at((x, y), pants_color)

        # Arms (with animation)
        arm_offset = 0
        if animation_frame == "walk1":
            arm_offset = 1
        elif animation_frame == "walk2":
            arm_offset = -1

        # Left arm
        for y in range(body_y + 2, body_y + 8):
            sprite.set_at((7, y + arm_offset), skin_color)
            sprite.set_at((8, y + arm_offset), shirt_color)

        # Right arm
        for y in range(body_y + 2, body_y + 8):
            sprite.set_at((21, y - arm_offset), skin_color)
            sprite.set_at((20, y - arm_offset), shirt_color)

        # Legs (with walking animation)
        leg_offset = 0
        if animation_frame == "walk1":
            leg_offset = 1
        elif animation_frame == "walk2":
            leg_offset = -1

        # Left leg
        for y in range(body_y + 8, height - 2):
            sprite.set_at((12, y + leg_offset), pants_color)
        sprite.set_at((12, height - 2), boot_color)  # Boot
        sprite.set_at((11, height - 1), boot_color)

        # Right leg
        for y in range(body_y + 8, height - 2):
            sprite.set_at((17, y - leg_offset), pants_color)
        sprite.set_at((17, height - 2), boot_color)  # Boot
        sprite.set_at((18, height - 1), boot_color)

        return sprite

    def create_professional_zombie_sprite(self, width, height, animation_frame):
        """Create detailed zombie sprite"""
        sprite = pygame.Surface((width, height), pygame.SRCALPHA)

        # Zombie colors
        zombie_skin = (85, 107, 47)
        rotten_skin = (65, 87, 27)
        blood_color = (139, 0, 0)
        torn_clothes = (40, 40, 40)

        # Decayed head
        head_y = 2
        for y in range(head_y, head_y + 8):
            for x in range(8, 22):
                if (x + y) % 3 == 0:
                    sprite.set_at((x, y), rotten_skin)
                else:
                    sprite.set_at((x, y), zombie_skin)

        # Glowing red eyes
        sprite.set_at((11, head_y + 3), (255, 0, 0))
        sprite.set_at((18, head_y + 3), (255, 0, 0))

        # Blood dripping from mouth
        for i in range(3):
            sprite.set_at((14, head_y + 6 + i), blood_color)

        # Torn body
        body_y = 10
        for y in range(body_y, body_y + 12):
            for x in range(10, 20):
                if (x + y) % 4 == 0:
                    sprite.set_at((x, y), torn_clothes)
                else:
                    sprite.set_at((x, y), zombie_skin)

        # Shambling arms
        arm_offset = 0
        if animation_frame == "walk1":
            arm_offset = 2
        elif animation_frame == "walk2":
            arm_offset = -1

        # Drooping arms
        for y in range(body_y + 2, body_y + 10):
            sprite.set_at((6, y + arm_offset), zombie_skin)
            sprite.set_at((22, y - arm_offset), zombie_skin)

        # Dragging legs
        leg_offset = 0
        if animation_frame == "walk1":
            leg_offset = 1
        elif animation_frame == "walk2":
            leg_offset = -1

        for y in range(body_y + 8, height):
            sprite.set_at((12, y + leg_offset), zombie_skin)
            sprite.set_at((17, y - leg_offset), zombie_skin)

        return sprite

    def create_professional_survivor_sprite(self, width, height):
        """Create detailed survivor NPC sprite"""
        sprite = pygame.Surface((width, height), pygame.SRCALPHA)

        # Survivor colors (different from player)
        skin_color = (255, 200, 160)
        hair_color = (139, 69, 19)
        jacket_color = (160, 82, 45)
        pants_color = (25, 25, 112)

        # Similar structure to player but different colors
        # Head
        head_y = 2
        for y in range(head_y, head_y + 8):
            for x in range(8, 22):
                if y < head_y + 2:
                    sprite.set_at((x, y), hair_color)
                elif y < head_y + 6:
                    sprite.set_at((x, y), skin_color)

        # Eyes and face
        sprite.set_at((11, head_y + 3), (0, 0, 0))
        sprite.set_at((18, head_y + 3), (0, 0, 0))
        sprite.set_at((14, head_y + 5), (150, 75, 75))

        # Body with jacket
        body_y = 10
        for y in range(body_y, body_y + 12):
            for x in range(10, 20):
                if y < body_y + 8:
                    sprite.set_at((x, y), jacket_color)
                else:
                    sprite.set_at((x, y), pants_color)

        return sprite
    
    def generate_professional_item_sprites(self):
        """Generate professional item sprites"""

        # Professional Wood Item
        wood_item = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Draw wood log with bark texture
        for y in range(24):
            for x in range(24):
                if 4 <= x <= 19 and 6 <= y <= 17:
                    # Wood interior
                    if (x + y) % 4 < 2:
                        wood_item.set_at((x, y), (139, 90, 43))
                    else:
                        wood_item.set_at((x, y), (160, 110, 60))
                elif 2 <= x <= 21 and 4 <= y <= 19:
                    # Bark
                    wood_item.set_at((x, y), (101, 67, 33))

        # Add wood rings
        pygame.draw.circle(wood_item, (80, 50, 20), (12, 12), 8, 1)
        pygame.draw.circle(wood_item, (80, 50, 20), (12, 12), 5, 1)
        self.sprites[ITEM_WOOD] = wood_item

        # Professional Stone Item
        stone_item = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Create irregular stone shape
        stone_points = [(4, 8), (8, 4), (16, 6), (20, 12), (18, 20), (10, 22), (6, 18)]
        pygame.draw.polygon(stone_item, (128, 128, 128), stone_points)
        pygame.draw.polygon(stone_item, (100, 100, 100), stone_points, 2)

        # Add stone texture
        for i in range(8):
            x, y = (i * 3 + 6) % 18 + 3, (i * 5 + 8) % 14 + 5
            stone_item.set_at((x, y), (148, 148, 148))
        self.sprites[ITEM_STONE] = stone_item

        # Professional Stick Item
        stick_item = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Draw wooden stick with texture
        for i in range(20):
            x = 4 + i
            y = 4 + i
            if x < 24 and y < 24:
                stick_item.set_at((x, y), (139, 90, 43))
                stick_item.set_at((x+1, y), (160, 110, 60))
                stick_item.set_at((x, y+1), (101, 67, 33))
        self.sprites[ITEM_STICK] = stick_item

        # Professional Coal Item
        coal_item = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Create coal chunk shape
        coal_points = [(6, 10), (10, 6), (14, 8), (18, 12), (16, 18), (12, 20), (8, 16)]
        pygame.draw.polygon(coal_item, (25, 25, 25), coal_points)
        pygame.draw.polygon(coal_item, (0, 0, 0), coal_points, 2)

        # Add coal shine
        for i in range(4):
            x, y = coal_points[i * 2][0], coal_points[i * 2][1]
            coal_item.set_at((x, y), (60, 60, 60))
        self.sprites[ITEM_COAL] = coal_item

        # Professional Iron Ingot
        iron_item = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Draw metallic ingot
        pygame.draw.rect(iron_item, (169, 169, 169), (6, 8, 12, 8))
        pygame.draw.rect(iron_item, (192, 192, 192), (6, 8, 12, 3))  # Top highlight
        pygame.draw.rect(iron_item, (128, 128, 128), (6, 13, 12, 3))  # Bottom shadow
        pygame.draw.rect(iron_item, (105, 105, 105), (6, 8, 12, 8), 1)  # Outline
        self.sprites[ITEM_IRON] = iron_item

        # Professional Food Item (Canned Food)
        food_item = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Draw can
        pygame.draw.rect(food_item, (220, 20, 60), (7, 6, 10, 12))
        pygame.draw.rect(food_item, (255, 100, 120), (7, 6, 10, 4))  # Label
        pygame.draw.rect(food_item, (180, 180, 180), (7, 5, 10, 2))  # Top
        pygame.draw.rect(food_item, (160, 160, 160), (7, 17, 10, 2))  # Bottom

        # Add label text effect
        food_item.set_at((9, 8), (255, 255, 255))
        food_item.set_at((11, 8), (255, 255, 255))
        food_item.set_at((13, 8), (255, 255, 255))
        food_item.set_at((15, 8), (255, 255, 255))
        self.sprites[ITEM_FOOD] = food_item

        # Generate tool sprites
        self.generate_professional_tool_sprites()

    def generate_professional_tool_sprites(self):
        """Generate professional tool sprites"""

        # Wooden Axe
        axe_wood = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Handle
        pygame.draw.rect(axe_wood, (139, 90, 43), (11, 4, 2, 16))
        # Axe head
        axe_points = [(8, 6), (14, 4), (16, 8), (14, 12), (8, 10)]
        pygame.draw.polygon(axe_wood, (101, 67, 33), axe_points)
        pygame.draw.polygon(axe_wood, (80, 50, 20), axe_points, 1)
        self.sprites[TOOL_AXE_WOOD] = axe_wood

        # Stone Axe
        axe_stone = pygame.Surface((24, 24), pygame.SRCALPHA)
        pygame.draw.rect(axe_stone, (139, 90, 43), (11, 4, 2, 16))
        pygame.draw.polygon(axe_stone, (128, 128, 128), axe_points)
        pygame.draw.polygon(axe_stone, (100, 100, 100), axe_points, 1)
        self.sprites[TOOL_AXE_STONE] = axe_stone

        # Iron Axe
        axe_iron = pygame.Surface((24, 24), pygame.SRCALPHA)
        pygame.draw.rect(axe_iron, (139, 90, 43), (11, 4, 2, 16))
        pygame.draw.polygon(axe_iron, (169, 169, 169), axe_points)
        pygame.draw.polygon(axe_iron, (192, 192, 192), [(8, 6), (12, 5), (14, 7)])  # Shine
        self.sprites[TOOL_AXE_IRON] = axe_iron

        # Generate sword sprites
        sword_points = [(11, 4), (13, 4), (14, 14), (12, 16), (10, 14)]

        # Wooden Sword
        sword_wood = pygame.Surface((24, 24), pygame.SRCALPHA)
        pygame.draw.polygon(sword_wood, (139, 90, 43), sword_points)
        pygame.draw.rect(sword_wood, (101, 67, 33), (10, 16, 4, 4))  # Handle
        self.sprites[TOOL_SWORD_WOOD] = sword_wood

        # Stone Sword
        sword_stone = pygame.Surface((24, 24), pygame.SRCALPHA)
        pygame.draw.polygon(sword_stone, (128, 128, 128), sword_points)
        pygame.draw.rect(sword_stone, (101, 67, 33), (10, 16, 4, 4))
        self.sprites[TOOL_SWORD_STONE] = sword_stone

        # Iron Sword
        sword_iron = pygame.Surface((24, 24), pygame.SRCALPHA)
        pygame.draw.polygon(sword_iron, (169, 169, 169), sword_points)
        pygame.draw.line(sword_iron, (192, 192, 192), (12, 5), (12, 14))  # Blade shine
        pygame.draw.rect(sword_iron, (101, 67, 33), (10, 16, 4, 4))
        self.sprites[TOOL_SWORD_IRON] = sword_iron
    
    def generate_professional_ui_sprites(self):
        """Generate professional UI sprites"""

        # Professional Inventory Slot
        slot = pygame.Surface((48, 48), pygame.SRCALPHA)
        # Create beveled slot appearance
        pygame.draw.rect(slot, (60, 60, 60), (2, 2, 44, 44))
        pygame.draw.rect(slot, (40, 40, 40), (0, 0, 48, 48), 2)
        pygame.draw.rect(slot, (80, 80, 80), (1, 1, 46, 46), 1)

        # Inner shadow
        pygame.draw.line(slot, (30, 30, 30), (3, 3), (44, 3))
        pygame.draw.line(slot, (30, 30, 30), (3, 3), (3, 44))
        self.sprites['inventory_slot'] = slot

        # Professional Selected Slot
        selected_slot = pygame.Surface((48, 48), pygame.SRCALPHA)
        pygame.draw.rect(selected_slot, (80, 80, 60), (2, 2, 44, 44))
        pygame.draw.rect(selected_slot, (255, 215, 0), (0, 0, 48, 48), 3)
        pygame.draw.rect(selected_slot, (255, 255, 100), (1, 1, 46, 46), 1)

        # Glow effect
        for i in range(3):
            alpha = 100 - i * 30
            glow_surf = pygame.Surface((48 + i*2, 48 + i*2), pygame.SRCALPHA)
            pygame.draw.rect(glow_surf, (255, 215, 0, alpha), (0, 0, 48 + i*2, 48 + i*2), 1)

        self.sprites['selected_slot'] = selected_slot

        # Professional Health Bar Components
        health_bar_bg = pygame.Surface((200, 24), pygame.SRCALPHA)
        pygame.draw.rect(health_bar_bg, (40, 40, 40), (0, 0, 200, 24))
        pygame.draw.rect(health_bar_bg, (20, 20, 20), (0, 0, 200, 24), 2)
        self.sprites['health_bar_bg'] = health_bar_bg

        health_bar_fill = pygame.Surface((196, 20), pygame.SRCALPHA)
        # Gradient health bar
        for x in range(196):
            for y in range(20):
                red_intensity = min(255, 150 + (x * 105) // 196)
                green_intensity = max(0, 255 - (x * 255) // 196)
                health_bar_fill.set_at((x, y), (red_intensity, green_intensity, 0))
        self.sprites['health_bar_fill'] = health_bar_fill

        # Professional Button
        button = pygame.Surface((120, 40), pygame.SRCALPHA)
        pygame.draw.rect(button, (70, 130, 180), (0, 0, 120, 40))
        pygame.draw.rect(button, (100, 160, 210), (2, 2, 116, 36))
        pygame.draw.rect(button, (40, 100, 150), (0, 0, 120, 40), 2)

        # Button highlight
        pygame.draw.line(button, (130, 190, 240), (3, 3), (116, 3))
        pygame.draw.line(button, (130, 190, 240), (3, 3), (3, 36))
        self.sprites['button'] = button

    def generate_effect_sprites(self):
        """Generate particle and effect sprites"""

        # Blood particle
        blood_particle = pygame.Surface((4, 4), pygame.SRCALPHA)
        pygame.draw.circle(blood_particle, (139, 0, 0), (2, 2), 2)
        pygame.draw.circle(blood_particle, (200, 50, 50), (1, 1), 1)
        self.sprites['blood_particle'] = blood_particle

        # Dust particle
        dust_particle = pygame.Surface((3, 3), pygame.SRCALPHA)
        pygame.draw.circle(dust_particle, (139, 126, 102), (1, 1), 1)
        self.sprites['dust_particle'] = dust_particle

        # Sparkle effect
        sparkle = pygame.Surface((8, 8), pygame.SRCALPHA)
        pygame.draw.line(sparkle, (255, 255, 255), (4, 0), (4, 7))
        pygame.draw.line(sparkle, (255, 255, 255), (0, 4), (7, 4))
        pygame.draw.line(sparkle, (255, 255, 255), (1, 1), (6, 6))
        pygame.draw.line(sparkle, (255, 255, 255), (6, 1), (1, 6))
        self.sprites['sparkle'] = sparkle

        # Damage number background
        damage_bg = pygame.Surface((32, 16), pygame.SRCALPHA)
        pygame.draw.rect(damage_bg, (0, 0, 0, 128), (0, 0, 32, 16))
        pygame.draw.rect(damage_bg, (255, 255, 255, 200), (0, 0, 32, 16), 1)
        self.sprites['damage_bg'] = damage_bg
    
    def get_sprite(self, sprite_id):
        """Get sprite by ID"""
        return self.sprites.get(sprite_id, None)
    
    def get_block_sprite(self, block_type):
        """Get block sprite"""
        return self.sprites.get(block_type, None)
