"""
Main Game class - handles the core game loop and state management
"""

import pygame
import sys
from src.constants import *
from src.world import World
from src.player import Player
from src.camera import Camera
from src.ui import UI

class Game:
    def __init__(self):
        """Initialize the game"""
        pygame.init()
        
        # Set up display
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Zombie Craft - Survive the Apocalypse")
        
        # Game clock
        self.clock = pygame.time.Clock()
        
        # Game state
        self.running = True
        self.paused = False
        
        # Initialize game components
        self.world = World()
        self.player = Player(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
        self.camera = Camera()
        self.ui = UI()
        
        # Time tracking
        self.game_time = 0  # Total game time in seconds
        self.is_day = True
        
    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.paused = not self.paused
                elif event.key == pygame.K_q:
                    self.running = False
                    
            # Handle player input
            self.player.handle_event(event, self.world)
    
    def update(self, dt):
        """Update game state"""
        if self.paused:
            return
            
        # Update game time
        self.game_time += dt
        
        # Update day/night cycle
        cycle_time = self.game_time % (DAY_LENGTH + NIGHT_LENGTH)
        self.is_day = cycle_time < DAY_LENGTH
        
        # Update game components
        self.player.update(dt, self.world)
        self.world.update(dt, self.player)
        self.camera.update(self.player)
        
    def render(self):
        """Render the game"""
        # Clear screen with atmosphere color
        if self.is_day:
            self.screen.fill((135, 206, 235))  # Sky blue
        else:
            self.screen.fill((25, 25, 112))  # Midnight blue
            
        # Render world
        self.world.render(self.screen, self.camera)
        
        # Render player
        self.player.render(self.screen, self.camera)
        
        # Render UI
        self.ui.render(self.screen, self.player, self.is_day, self.game_time)
        
        # Show pause screen
        if self.paused:
            self.render_pause_screen()
            
        pygame.display.flip()
    
    def render_pause_screen(self):
        """Render pause overlay"""
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))
        
        font = pygame.font.Font(None, 74)
        text = font.render("PAUSED", True, WHITE)
        text_rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        self.screen.blit(text, text_rect)
        
        font_small = pygame.font.Font(None, 36)
        text_small = font_small.render("Press ESC to resume", True, WHITE)
        text_small_rect = text_small.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 50))
        self.screen.blit(text_small, text_small_rect)
    
    def run(self):
        """Main game loop"""
        print("Starting Zombie Craft...")
        
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # Delta time in seconds
            
            self.handle_events()
            self.update(dt)
            self.render()
        
        print("Game ended.")
        pygame.quit()
