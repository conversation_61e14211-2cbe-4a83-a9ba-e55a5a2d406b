"""
Camera class - handles camera movement and viewport management
"""

from src.constants import *

class Camera:
    def __init__(self):
        """Initialize the camera"""
        self.x = 0
        self.y = 0
        
    def update(self, player):
        """Update camera to follow player"""
        # Center camera on player
        self.x = player.x - SCREEN_WIDTH // 2
        self.y = player.y - SCREEN_HEIGHT // 2
