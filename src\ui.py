"""
UI class - handles user interface elements, HUD, and menus
"""

import pygame
from src.constants import *

class UI:
    def __init__(self):
        """Initialize the UI"""
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
    def render(self, screen, player, is_day, game_time):
        """Render the UI elements"""
        # Health bar
        self.render_health_bar(screen, player)
        
        # Hunger bar
        self.render_hunger_bar(screen, player)
        
        # Inventory
        self.render_inventory(screen, player)
        
        # Day/Night indicator
        self.render_time_indicator(screen, is_day, game_time)
        
        # Instructions
        self.render_instructions(screen)
    
    def render_health_bar(self, screen, player):
        """Render player health bar"""
        bar_width = 200
        bar_height = 20
        bar_x = 20
        bar_y = 20
        
        # Background
        pygame.draw.rect(screen, DARK_GRAY, (bar_x, bar_y, bar_width, bar_height))
        
        # Health
        health_width = int((player.health / player.max_health) * bar_width)
        pygame.draw.rect(screen, RED, (bar_x, bar_y, health_width, bar_height))
        
        # Border
        pygame.draw.rect(screen, WHITE, (bar_x, bar_y, bar_width, bar_height), 2)
        
        # Text
        text = self.small_font.render(f"Health: {int(player.health)}/{player.max_health}", True, WHITE)
        screen.blit(text, (bar_x, bar_y - 20))
    
    def render_hunger_bar(self, screen, player):
        """Render player hunger bar"""
        bar_width = 200
        bar_height = 20
        bar_x = 20
        bar_y = 60
        
        # Background
        pygame.draw.rect(screen, DARK_GRAY, (bar_x, bar_y, bar_width, bar_height))
        
        # Hunger
        hunger_width = int((player.hunger / player.max_hunger) * bar_width)
        pygame.draw.rect(screen, YELLOW, (bar_x, bar_y, hunger_width, bar_height))
        
        # Border
        pygame.draw.rect(screen, WHITE, (bar_x, bar_y, bar_width, bar_height), 2)
        
        # Text
        text = self.small_font.render(f"Hunger: {int(player.hunger)}/{player.max_hunger}", True, WHITE)
        screen.blit(text, (bar_x, bar_y - 20))
    
    def render_inventory(self, screen, player):
        """Render player inventory"""
        inv_x = 20
        inv_y = 120
        
        text = self.small_font.render("Inventory:", True, WHITE)
        screen.blit(text, (inv_x, inv_y))
        
        y_offset = 25
        for item_type, count in player.inventory.items():
            item_name = self.get_item_name(item_type)
            color = WHITE
            if item_type == player.selected_item:
                color = YELLOW
                
            text = self.small_font.render(f"{item_name}: {count}", True, color)
            screen.blit(text, (inv_x, inv_y + y_offset))
            y_offset += 20
    
    def render_time_indicator(self, screen, is_day, game_time):
        """Render day/night indicator"""
        time_text = "DAY" if is_day else "NIGHT"
        color = YELLOW if is_day else BLUE
        
        text = self.font.render(time_text, True, color)
        text_rect = text.get_rect()
        text_rect.topright = (SCREEN_WIDTH - 20, 20)
        screen.blit(text, text_rect)
        
        # Time display
        minutes = int(game_time // 60)
        seconds = int(game_time % 60)
        time_str = f"{minutes:02d}:{seconds:02d}"
        time_text = self.small_font.render(time_str, True, WHITE)
        time_rect = time_text.get_rect()
        time_rect.topright = (SCREEN_WIDTH - 20, 60)
        screen.blit(time_text, time_rect)
    
    def render_instructions(self, screen):
        """Render control instructions"""
        instructions = [
            "WASD/Arrow Keys: Move",
            "Left Click: Break Block",
            "Right Click: Place Block",
            "1-4: Select Item",
            "ESC: Pause",
            "Q: Quit"
        ]
        
        y_start = SCREEN_HEIGHT - len(instructions) * 20 - 20
        for i, instruction in enumerate(instructions):
            text = self.small_font.render(instruction, True, WHITE)
            screen.blit(text, (20, y_start + i * 20))
    
    def get_item_name(self, item_type):
        """Get display name for item type"""
        names = {
            BLOCK_GRASS: "Grass",
            BLOCK_DIRT: "Dirt", 
            BLOCK_STONE: "Stone",
            BLOCK_WOOD: "Wood",
            BLOCK_LEAVES: "Leaves",
            BLOCK_WATER: "Water",
            BLOCK_SAND: "Sand"
        }
        return names.get(item_type, f"Item {item_type}")
