"""
UI class - handles user interface elements, HUD, and menus
"""

import pygame
from src.constants import *

class UI:
    def __init__(self):
        """Initialize the UI"""
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
    def render(self, screen, player, is_day, game_time, day_count, zombie_count, sprite_manager):
        """Render the UI elements"""
        # Health bar
        self.render_health_bar(screen, player)

        # Hunger bar
        self.render_hunger_bar(screen, player)

        # Thirst bar
        self.render_thirst_bar(screen, player)

        # Hotbar
        self.render_hotbar(screen, player, sprite_manager)

        # Day/Night indicator
        self.render_time_indicator(screen, is_day, game_time, day_count)

        # Zombie counter
        self.render_zombie_counter(screen, zombie_count)

        # Instructions
        self.render_instructions(screen)
    
    def render_health_bar(self, screen, player):
        """Render player health bar"""
        bar_width = 200
        bar_height = 20
        bar_x = 20
        bar_y = 20
        
        # Background
        pygame.draw.rect(screen, DARK_GRAY, (bar_x, bar_y, bar_width, bar_height))
        
        # Health
        health_width = int((player.health / player.max_health) * bar_width)
        pygame.draw.rect(screen, RED, (bar_x, bar_y, health_width, bar_height))
        
        # Border
        pygame.draw.rect(screen, WHITE, (bar_x, bar_y, bar_width, bar_height), 2)
        
        # Text
        text = self.small_font.render(f"Health: {int(player.health)}/{player.max_health}", True, WHITE)
        screen.blit(text, (bar_x, bar_y - 20))
    
    def render_hunger_bar(self, screen, player):
        """Render player hunger bar"""
        bar_width = 200
        bar_height = 20
        bar_x = 20
        bar_y = 60

        # Background
        pygame.draw.rect(screen, DARK_GRAY, (bar_x, bar_y, bar_width, bar_height))

        # Hunger
        hunger_width = int((player.hunger / player.max_hunger) * bar_width)
        pygame.draw.rect(screen, ORANGE, (bar_x, bar_y, hunger_width, bar_height))

        # Border
        pygame.draw.rect(screen, WHITE, (bar_x, bar_y, bar_width, bar_height), 2)

        # Text
        text = self.small_font.render(f"Hunger: {int(player.hunger)}/{player.max_hunger}", True, WHITE)
        screen.blit(text, (bar_x, bar_y - 20))

    def render_thirst_bar(self, screen, player):
        """Render player thirst bar"""
        bar_width = 200
        bar_height = 20
        bar_x = 20
        bar_y = 100

        # Background
        pygame.draw.rect(screen, DARK_GRAY, (bar_x, bar_y, bar_width, bar_height))

        # Thirst
        thirst_width = int((player.thirst / player.max_thirst) * bar_width)
        pygame.draw.rect(screen, BLUE, (bar_x, bar_y, thirst_width, bar_height))

        # Border
        pygame.draw.rect(screen, WHITE, (bar_x, bar_y, bar_width, bar_height), 2)

        # Text
        text = self.small_font.render(f"Thirst: {int(player.thirst)}/{player.max_thirst}", True, WHITE)
        screen.blit(text, (bar_x, bar_y - 20))
    
    def render_hotbar(self, screen, player, sprite_manager):
        """Render player hotbar"""
        hotbar_x = SCREEN_WIDTH // 2 - (player.hotbar_size * 45) // 2
        hotbar_y = SCREEN_HEIGHT - 60
        slot_size = 40

        items = list(player.inventory.items())

        for i in range(player.hotbar_size):
            x = hotbar_x + i * 45
            y = hotbar_y

            # Slot background
            if i == player.selected_slot:
                slot_sprite = sprite_manager.get_sprite('selected_slot')
            else:
                slot_sprite = sprite_manager.get_sprite('inventory_slot')

            if slot_sprite:
                screen.blit(slot_sprite, (x, y))

            # Item in slot
            if i < len(items):
                item_type, count = items[i]

                # Item sprite
                item_sprite = sprite_manager.get_sprite(item_type)
                if item_sprite:
                    # Scale and center item sprite
                    scaled_sprite = pygame.transform.scale(item_sprite, (24, 24))
                    screen.blit(scaled_sprite, (x + 8, y + 8))

                # Item count
                if count > 1:
                    count_text = self.small_font.render(str(count), True, WHITE)
                    screen.blit(count_text, (x + slot_size - 15, y + slot_size - 15))

            # Slot number
            number_text = self.small_font.render(str(i + 1), True, WHITE)
            screen.blit(number_text, (x + 2, y - 18))
    
    def render_time_indicator(self, screen, is_day, game_time, day_count):
        """Render day/night indicator"""
        time_text = "DAY" if is_day else "NIGHT"
        color = YELLOW if is_day else PURPLE

        text = self.font.render(time_text, True, color)
        text_rect = text.get_rect()
        text_rect.topright = (SCREEN_WIDTH - 20, 20)
        screen.blit(text, text_rect)

        # Day counter
        day_text = self.small_font.render(f"Day {day_count}", True, WHITE)
        day_rect = day_text.get_rect()
        day_rect.topright = (SCREEN_WIDTH - 20, 50)
        screen.blit(day_text, day_rect)

        # Time display
        minutes = int(game_time // 60)
        seconds = int(game_time % 60)
        time_str = f"{minutes:02d}:{seconds:02d}"
        time_text = self.small_font.render(time_str, True, WHITE)
        time_rect = time_text.get_rect()
        time_rect.topright = (SCREEN_WIDTH - 20, 75)
        screen.blit(time_text, time_rect)

    def render_zombie_counter(self, screen, zombie_count):
        """Render zombie counter"""
        zombie_text = f"Zombies: {zombie_count}"
        text = self.small_font.render(zombie_text, True, RED)
        text_rect = text.get_rect()
        text_rect.topright = (SCREEN_WIDTH - 20, 100)
        screen.blit(text, text_rect)
    
    def render_instructions(self, screen):
        """Render control instructions"""
        instructions = [
            "WASD: Move",
            "Left Click: Break Block",
            "Right Click: Place Block",
            "1-9: Select Hotbar Slot",
            "F: Use/Eat Item",
            "SPACE: Attack (with sword)",
            "I: Inventory",
            "C: Crafting",
            "ESC: Pause"
        ]

        y_start = SCREEN_HEIGHT - len(instructions) * 18 - 120
        for i, instruction in enumerate(instructions):
            text = self.small_font.render(instruction, True, WHITE)
            screen.blit(text, (20, y_start + i * 18))
    
    def get_item_name(self, item_type):
        """Get display name for item type"""
        names = {
            BLOCK_GRASS: "Grass",
            BLOCK_DIRT: "Dirt", 
            BLOCK_STONE: "Stone",
            BLOCK_WOOD: "Wood",
            BLOCK_LEAVES: "Leaves",
            BLOCK_WATER: "Water",
            BLOCK_SAND: "Sand"
        }
        return names.get(item_type, f"Item {item_type}")
