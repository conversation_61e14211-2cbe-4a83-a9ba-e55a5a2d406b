"""
Player class - handles player character, movement, inventory, and interactions
"""

import pygame
import math
from src.constants import *

class Player:
    def __init__(self, x, y):
        """Initialize the player"""
        self.x = x
        self.y = y
        self.width = TILE_SIZE - 4
        self.height = TILE_SIZE - 4

        # Movement
        self.vel_x = 0
        self.vel_y = 0
        self.speed = PLAYER_SPEED

        # Stats
        self.health = 100
        self.max_health = 100
        self.hunger = 100
        self.max_hunger = 100
        self.thirst = 100
        self.max_thirst = 100

        # Inventory
        self.inventory = {}
        self.selected_slot = 0
        self.hotbar_size = 9

        # Tools and equipment
        self.equipped_tool = None
        self.tool_durability = {}

        # Animation and state
        self.facing_direction = "right"
        self.animation_timer = 0
        self.is_moving = False

        # Combat
        self.attack_cooldown = 0
        self.invulnerability_timer = 0
        
    def handle_event(self, event, world, camera, mission_manager=None):
        """Handle player input events"""
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click - break block
                self.break_block(event.pos, world, camera, mission_manager)
            elif event.button == 3:  # Right click - place block
                self.place_block(event.pos, world, camera, mission_manager)

        elif event.type == pygame.KEYDOWN:
            # Hotbar selection
            if pygame.K_1 <= event.key <= pygame.K_9:
                self.selected_slot = event.key - pygame.K_1

            # Use item/eat food
            elif event.key == pygame.K_f:
                self.use_selected_item()
    
    def break_block(self, mouse_pos, world, camera, mission_manager=None):
        """Break a block at mouse position"""
        # Convert screen coordinates to world coordinates
        world_x = mouse_pos[0] + camera.x
        world_y = mouse_pos[1] + camera.y

        # Convert to tile coordinates
        tile_x = int(world_x // TILE_SIZE)
        tile_y = int(world_y // TILE_SIZE)

        # Check if within reach
        player_tile_x = int(self.x // TILE_SIZE)
        player_tile_y = int(self.y // TILE_SIZE)

        distance = math.sqrt((tile_x - player_tile_x)**2 + (tile_y - player_tile_y)**2)
        if distance <= PLAYER_REACH:
            block_type = world.get_block(tile_x, tile_y)
            if block_type != BLOCK_AIR:
                # Check if we have the right tool
                break_time = self.get_break_time(block_type)

                # Break the block
                world.set_block(tile_x, tile_y, BLOCK_AIR)

                # Add to inventory based on block type
                if block_type == BLOCK_COAL_ORE:
                    self.add_to_inventory(ITEM_COAL, 1)
                    if mission_manager:
                        mission_manager.on_block_collected(BLOCK_COAL_ORE, 1)
                elif block_type == BLOCK_IRON_ORE:
                    self.add_to_inventory(BLOCK_IRON_ORE, 1)
                    if mission_manager:
                        mission_manager.on_block_collected(BLOCK_IRON_ORE, 1)
                else:
                    self.add_to_inventory(block_type, 1)
                    if mission_manager:
                        mission_manager.on_block_collected(block_type, 1)

                # Damage tool if used
                if self.equipped_tool:
                    self.damage_tool(self.equipped_tool, 1)
    
    def place_block(self, mouse_pos, world, camera, mission_manager=None):
        """Place a block at mouse position"""
        selected_item = self.get_selected_item()
        if selected_item is None:
            return

        # Convert screen coordinates to world coordinates
        world_x = mouse_pos[0] + camera.x
        world_y = mouse_pos[1] + camera.y

        # Convert to tile coordinates
        tile_x = int(world_x // TILE_SIZE)
        tile_y = int(world_y // TILE_SIZE)

        # Check if within reach
        player_tile_x = int(self.x // TILE_SIZE)
        player_tile_y = int(self.y // TILE_SIZE)

        distance = math.sqrt((tile_x - player_tile_x)**2 + (tile_y - player_tile_y)**2)
        if distance <= PLAYER_REACH and world.get_block(tile_x, tile_y) == BLOCK_AIR:
            # Check if item is placeable (block type)
            if selected_item < 100:  # Block types are < 100
                if selected_item in self.inventory and self.inventory[selected_item] > 0:
                    world.set_block(tile_x, tile_y, selected_item)
                    self.inventory[selected_item] -= 1
                    if self.inventory[selected_item] <= 0:
                        del self.inventory[selected_item]

                    if mission_manager:
                        mission_manager.on_block_placed(selected_item, 1)
    
    def get_selected_item(self):
        """Get currently selected item from hotbar"""
        hotbar_items = list(self.inventory.keys())
        if self.selected_slot < len(hotbar_items):
            return hotbar_items[self.selected_slot]
        return None

    def get_break_time(self, block_type):
        """Get time needed to break a block with current tool"""
        # Tool effectiveness against different blocks
        tool_effectiveness = {
            TOOL_AXE_WOOD: {BLOCK_WOOD: 0.5, BLOCK_LEAVES: 0.1},
            TOOL_AXE_STONE: {BLOCK_WOOD: 0.3, BLOCK_LEAVES: 0.1},
            TOOL_AXE_IRON: {BLOCK_WOOD: 0.2, BLOCK_LEAVES: 0.1},
            TOOL_PICKAXE_WOOD: {BLOCK_STONE: 1.0, BLOCK_COAL_ORE: 1.2, BLOCK_IRON_ORE: 2.0},
            TOOL_PICKAXE_STONE: {BLOCK_STONE: 0.6, BLOCK_COAL_ORE: 0.8, BLOCK_IRON_ORE: 1.5},
            TOOL_PICKAXE_IRON: {BLOCK_STONE: 0.4, BLOCK_COAL_ORE: 0.5, BLOCK_IRON_ORE: 1.0},
        }

        if self.equipped_tool and self.equipped_tool in tool_effectiveness:
            return tool_effectiveness[self.equipped_tool].get(block_type, 1.0)
        return 1.0  # Default break time

    def damage_tool(self, tool_type, damage):
        """Damage a tool"""
        max_durability = {
            TOOL_AXE_WOOD: 30, TOOL_AXE_STONE: 60, TOOL_AXE_IRON: 120,
            TOOL_PICKAXE_WOOD: 30, TOOL_PICKAXE_STONE: 60, TOOL_PICKAXE_IRON: 120,
            TOOL_SWORD_WOOD: 30, TOOL_SWORD_STONE: 60, TOOL_SWORD_IRON: 120,
        }

        if tool_type not in self.tool_durability:
            self.tool_durability[tool_type] = max_durability.get(tool_type, 30)

        self.tool_durability[tool_type] -= damage
        if self.tool_durability[tool_type] <= 0:
            # Tool breaks
            if tool_type in self.inventory:
                self.inventory[tool_type] -= 1
                if self.inventory[tool_type] <= 0:
                    del self.inventory[tool_type]
            del self.tool_durability[tool_type]
            if self.equipped_tool == tool_type:
                self.equipped_tool = None

    def use_selected_item(self):
        """Use the currently selected item"""
        selected_item = self.get_selected_item()
        if selected_item == ITEM_FOOD and selected_item in self.inventory:
            # Eat food
            self.hunger = min(self.max_hunger, self.hunger + 25)
            self.health = min(self.max_health, self.health + 10)
            self.inventory[selected_item] -= 1
            if self.inventory[selected_item] <= 0:
                del self.inventory[selected_item]
        elif selected_item in [TOOL_AXE_WOOD, TOOL_AXE_STONE, TOOL_AXE_IRON,
                              TOOL_PICKAXE_WOOD, TOOL_PICKAXE_STONE, TOOL_PICKAXE_IRON,
                              TOOL_SWORD_WOOD, TOOL_SWORD_STONE, TOOL_SWORD_IRON]:
            # Equip tool
            self.equipped_tool = selected_item

    def add_to_inventory(self, item_type, amount):
        """Add items to inventory"""
        if item_type in self.inventory:
            self.inventory[item_type] += amount
        else:
            self.inventory[item_type] = amount

    def take_damage(self, damage):
        """Take damage from enemies"""
        if self.invulnerability_timer <= 0:
            self.health -= damage
            self.invulnerability_timer = 1.0  # 1 second of invulnerability
            if self.health <= 0:
                self.health = 0
                return True  # Player died
        return False
    
    def update(self, dt, world):
        """Update player state"""
        # Update timers
        self.attack_cooldown = max(0, self.attack_cooldown - dt)
        self.invulnerability_timer = max(0, self.invulnerability_timer - dt)
        self.animation_timer += dt

        # Handle movement input
        keys = pygame.key.get_pressed()
        self.vel_x = 0
        self.vel_y = 0

        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            self.vel_x = -self.speed
            self.facing_direction = "left"
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            self.vel_x = self.speed
            self.facing_direction = "right"
        if keys[pygame.K_w] or keys[pygame.K_UP]:
            self.vel_y = -self.speed
        if keys[pygame.K_s] or keys[pygame.K_DOWN]:
            self.vel_y = self.speed

        # Check if moving
        self.is_moving = self.vel_x != 0 or self.vel_y != 0

        # Normalize diagonal movement
        if self.vel_x != 0 and self.vel_y != 0:
            self.vel_x *= 0.707  # 1/sqrt(2)
            self.vel_y *= 0.707

        # Apply movement
        new_x = self.x + self.vel_x * dt
        new_y = self.y + self.vel_y * dt

        # Collision detection with world bounds
        world_width = world.width * TILE_SIZE
        world_height = world.height * TILE_SIZE

        if new_x >= 0 and new_x <= world_width - self.width:
            self.x = new_x
        if new_y >= 0 and new_y <= world_height - self.height:
            self.y = new_y

        # Simple block collision (avoid water)
        tile_x = int(self.x // TILE_SIZE)
        tile_y = int(self.y // TILE_SIZE)

        block_type = world.get_block(tile_x, tile_y)
        if block_type == BLOCK_WATER:
            # Slow movement in water and lose thirst slower
            self.speed = PLAYER_SPEED * 0.5
            self.thirst = min(self.max_thirst, self.thirst + 10 * dt)
        else:
            self.speed = PLAYER_SPEED

        # Decrease hunger and thirst over time
        self.hunger -= 8 * dt
        self.thirst -= 12 * dt

        # Clamp values
        self.hunger = max(0, self.hunger)
        self.thirst = max(0, self.thirst)

        # Lose health when hungry or thirsty
        if self.hunger <= 0:
            self.health -= 15 * dt
        if self.thirst <= 0:
            self.health -= 20 * dt

        # Regenerate health slowly when well-fed
        if self.hunger > 80 and self.thirst > 80 and self.health < self.max_health:
            self.health += 5 * dt

        # Clamp health
        self.health = max(0, min(self.max_health, self.health))
    
    def render(self, screen, camera, sprite_manager):
        """Render the player"""
        screen_x = self.x - camera.x
        screen_y = self.y - camera.y

        # Get player sprite
        sprite = sprite_manager.get_sprite('player')
        if sprite:
            # Add walking animation
            offset = 0
            if self.is_moving:
                offset = int(math.sin(self.animation_timer * 8) * 2)

            # Flash red when taking damage
            if self.invulnerability_timer > 0:
                # Create red tinted sprite
                red_sprite = sprite.copy()
                red_sprite.fill((255, 100, 100), special_flags=pygame.BLEND_MULT)
                screen.blit(red_sprite, (screen_x, screen_y + offset))
            else:
                screen.blit(sprite, (screen_x, screen_y + offset))

        # Draw health bar above player
        bar_width = self.width
        bar_height = 4
        bar_x = screen_x
        bar_y = screen_y - 12

        # Health bar background
        pygame.draw.rect(screen, DARK_RED, (bar_x, bar_y, bar_width, bar_height))
        # Health bar
        health_width = int((self.health / self.max_health) * bar_width)
        pygame.draw.rect(screen, RED, (bar_x, bar_y, health_width, bar_height))

        # Equipment indicator
        if self.equipped_tool:
            tool_sprite = sprite_manager.get_sprite(self.equipped_tool)
            if tool_sprite:
                # Scale down tool sprite
                small_tool = pygame.transform.scale(tool_sprite, (12, 12))
                screen.blit(small_tool, (screen_x + self.width - 12, screen_y - 12))
