"""
Player class - handles player character, movement, inventory, and interactions
"""

import pygame
import math
from src.constants import *

class Player:
    def __init__(self, x, y):
        """Initialize the player"""
        self.x = x
        self.y = y
        self.width = TILE_SIZE - 4
        self.height = TILE_SIZE - 4
        
        # Movement
        self.vel_x = 0
        self.vel_y = 0
        self.speed = PLAYER_SPEED
        
        # Stats
        self.health = 100
        self.max_health = 100
        self.hunger = 100
        self.max_hunger = 100
        
        # Inventory
        self.inventory = {}
        self.selected_item = None
        
        # Animation
        self.facing_direction = "right"
        
        # Create player surface (simple colored rectangle for now)
        self.surface = pygame.Surface((self.width, self.height))
        self.surface.fill(BLUE)
        
    def handle_event(self, event, world):
        """Handle player input events"""
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click - break block
                self.break_block(event.pos, world)
            elif event.button == 3:  # Right click - place block
                self.place_block(event.pos, world)
    
    def break_block(self, mouse_pos, world):
        """Break a block at mouse position"""
        # Convert screen coordinates to world coordinates
        world_x = mouse_pos[0] + world.camera_x
        world_y = mouse_pos[1] + world.camera_y
        
        # Convert to tile coordinates
        tile_x = int(world_x // TILE_SIZE)
        tile_y = int(world_y // TILE_SIZE)
        
        # Check if within reach (simple distance check)
        player_tile_x = int(self.x // TILE_SIZE)
        player_tile_y = int(self.y // TILE_SIZE)
        
        distance = math.sqrt((tile_x - player_tile_x)**2 + (tile_y - player_tile_y)**2)
        if distance <= 3:  # Reach distance
            block_type = world.get_block(tile_x, tile_y)
            if block_type != BLOCK_AIR:
                world.set_block(tile_x, tile_y, BLOCK_AIR)
                self.add_to_inventory(block_type, 1)
    
    def place_block(self, mouse_pos, world):
        """Place a block at mouse position"""
        if self.selected_item is None:
            return
            
        # Convert screen coordinates to world coordinates
        world_x = mouse_pos[0] + world.camera_x
        world_y = mouse_pos[1] + world.camera_y
        
        # Convert to tile coordinates
        tile_x = int(world_x // TILE_SIZE)
        tile_y = int(world_y // TILE_SIZE)
        
        # Check if within reach
        player_tile_x = int(self.x // TILE_SIZE)
        player_tile_y = int(self.y // TILE_SIZE)
        
        distance = math.sqrt((tile_x - player_tile_x)**2 + (tile_y - player_tile_y)**2)
        if distance <= 3 and world.get_block(tile_x, tile_y) == BLOCK_AIR:
            if self.selected_item in self.inventory and self.inventory[self.selected_item] > 0:
                world.set_block(tile_x, tile_y, self.selected_item)
                self.inventory[self.selected_item] -= 1
                if self.inventory[self.selected_item] <= 0:
                    del self.inventory[self.selected_item]
                    self.selected_item = None
    
    def add_to_inventory(self, item_type, amount):
        """Add items to inventory"""
        if item_type in self.inventory:
            self.inventory[item_type] += amount
        else:
            self.inventory[item_type] = amount
    
    def update(self, dt, world):
        """Update player state"""
        # Handle movement input
        keys = pygame.key.get_pressed()
        self.vel_x = 0
        self.vel_y = 0
        
        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            self.vel_x = -self.speed
            self.facing_direction = "left"
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            self.vel_x = self.speed
            self.facing_direction = "right"
        if keys[pygame.K_w] or keys[pygame.K_UP]:
            self.vel_y = -self.speed
        if keys[pygame.K_s] or keys[pygame.K_DOWN]:
            self.vel_y = self.speed
            
        # Normalize diagonal movement
        if self.vel_x != 0 and self.vel_y != 0:
            self.vel_x *= 0.707  # 1/sqrt(2)
            self.vel_y *= 0.707
        
        # Apply movement
        new_x = self.x + self.vel_x * dt * 60  # Scale by 60 for consistent speed
        new_y = self.y + self.vel_y * dt * 60
        
        # Simple collision detection with world bounds
        if new_x >= 0 and new_x <= world.width * TILE_SIZE - self.width:
            self.x = new_x
        if new_y >= 0 and new_y <= world.height * TILE_SIZE - self.height:
            self.y = new_y
            
        # Handle item selection
        if keys[pygame.K_1] and BLOCK_GRASS in self.inventory:
            self.selected_item = BLOCK_GRASS
        elif keys[pygame.K_2] and BLOCK_DIRT in self.inventory:
            self.selected_item = BLOCK_DIRT
        elif keys[pygame.K_3] and BLOCK_STONE in self.inventory:
            self.selected_item = BLOCK_STONE
        elif keys[pygame.K_4] and BLOCK_WOOD in self.inventory:
            self.selected_item = BLOCK_WOOD
            
        # Decrease hunger over time
        self.hunger -= 5 * dt
        if self.hunger < 0:
            self.hunger = 0
            self.health -= 10 * dt  # Lose health when hungry
            
        # Clamp health
        if self.health < 0:
            self.health = 0
        elif self.health > self.max_health:
            self.health = self.max_health
    
    def render(self, screen, camera):
        """Render the player"""
        screen_x = self.x - camera.x
        screen_y = self.y - camera.y
        screen.blit(self.surface, (screen_x, screen_y))
        
        # Draw health bar above player
        bar_width = self.width
        bar_height = 4
        bar_x = screen_x
        bar_y = screen_y - 8
        
        # Background
        pygame.draw.rect(screen, RED, (bar_x, bar_y, bar_width, bar_height))
        # Health
        health_width = int((self.health / self.max_health) * bar_width)
        pygame.draw.rect(screen, GREEN, (bar_x, bar_y, health_width, bar_height))
