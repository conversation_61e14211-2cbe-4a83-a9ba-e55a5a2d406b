"""
Mission system for the game
"""

import random
from src.constants import *

class Mission:
    def __init__(self, mission_type, title, description, target_amount, reward_items=None):
        """Initialize a mission"""
        self.type = mission_type
        self.title = title
        self.description = description
        self.target_amount = target_amount
        self.current_progress = 0
        self.completed = False
        self.reward_items = reward_items or {}
        
    def update_progress(self, amount=1):
        """Update mission progress"""
        if not self.completed:
            self.current_progress += amount
            if self.current_progress >= self.target_amount:
                self.completed = True
                return True
        return False
    
    def get_progress_text(self):
        """Get progress as text"""
        return f"{self.current_progress}/{self.target_amount}"
    
    def is_completed(self):
        """Check if mission is completed"""
        return self.completed


class MissionManager:
    def __init__(self):
        """Initialize mission manager"""
        self.active_missions = []
        self.completed_missions = []
        self.available_missions = []
        self.max_active_missions = 3
        
        # Generate initial missions
        self.generate_starting_missions()
    
    def generate_starting_missions(self):
        """Generate starting missions for new players"""
        starting_missions = [
            Mission(MISSION_COLLECT, "Gather Wood", "Collect 10 wood blocks to start building", 10, 
                   {ITEM_STICK: 5, ITEM_FOOD: 2}),
            Mission(MISSION_COLLECT, "Mine Stone", "Mine 15 stone blocks for construction", 15,
                   {TOOL_PICKAXE_STONE: 1, ITEM_FOOD: 3}),
            Mission(MISSION_SURVIVE, "Survive the Night", "Survive for 2 minutes during nighttime", 120,
                   {ITEM_FOOD: 5, ITEM_COAL: 3}),
            Mission(MISSION_BUILD, "Build Shelter", "Place 20 blocks to build a shelter", 20,
                   {TOOL_AXE_STONE: 1, ITEM_FOOD: 4}),
            Mission(MISSION_KILL, "Zombie Hunter", "Kill 5 zombies to protect the area", 5,
                   {TOOL_SWORD_STONE: 1, ITEM_FOOD: 3})
        ]
        
        # Add first few missions as active
        for i, mission in enumerate(starting_missions[:2]):
            self.active_missions.append(mission)
        
        # Rest go to available missions
        self.available_missions.extend(starting_missions[2:])
    
    def update(self, dt, player, zombie_manager, is_night):
        """Update mission progress"""
        for mission in self.active_missions:
            if mission.completed:
                continue
                
            # Check mission progress based on type
            if mission.type == MISSION_SURVIVE and is_night:
                if mission.update_progress(dt):
                    self.complete_mission(mission, player)
    
    def on_block_collected(self, block_type, amount=1):
        """Called when player collects blocks"""
        for mission in self.active_missions:
            if mission.type == MISSION_COLLECT and not mission.completed:
                if (block_type == BLOCK_WOOD and "wood" in mission.title.lower()) or \
                   (block_type == BLOCK_STONE and "stone" in mission.title.lower()) or \
                   (block_type == BLOCK_COAL_ORE and "coal" in mission.title.lower()) or \
                   (block_type == BLOCK_IRON_ORE and "iron" in mission.title.lower()):
                    if mission.update_progress(amount):
                        self.complete_mission(mission, None)
    
    def on_block_placed(self, block_type, amount=1):
        """Called when player places blocks"""
        for mission in self.active_missions:
            if mission.type == MISSION_BUILD and not mission.completed:
                if mission.update_progress(amount):
                    self.complete_mission(mission, None)
    
    def on_zombie_killed(self):
        """Called when player kills a zombie"""
        for mission in self.active_missions:
            if mission.type == MISSION_KILL and not mission.completed:
                if mission.update_progress(1):
                    self.complete_mission(mission, None)
    
    def complete_mission(self, mission, player):
        """Complete a mission and give rewards"""
        mission.completed = True
        self.completed_missions.append(mission)
        
        # Give rewards to player
        if player and mission.reward_items:
            for item_type, amount in mission.reward_items.items():
                player.add_to_inventory(item_type, amount)
        
        # Try to add a new mission
        self.try_add_new_mission()
    
    def try_add_new_mission(self):
        """Try to add a new mission from available missions"""
        if len(self.active_missions) < self.max_active_missions and self.available_missions:
            new_mission = self.available_missions.pop(0)
            self.active_missions.append(new_mission)
            
            # Generate more missions if running low
            if len(self.available_missions) < 3:
                self.generate_random_missions()
    
    def generate_random_missions(self):
        """Generate random missions"""
        mission_templates = [
            # Collection missions
            (MISSION_COLLECT, "Wood Collector", "Collect {} wood blocks", 
             lambda: random.randint(15, 30), {ITEM_FOOD: 3, ITEM_STICK: 5}),
            (MISSION_COLLECT, "Stone Miner", "Mine {} stone blocks", 
             lambda: random.randint(20, 40), {ITEM_FOOD: 4, ITEM_COAL: 2}),
            (MISSION_COLLECT, "Coal Gatherer", "Mine {} coal ore", 
             lambda: random.randint(5, 15), {ITEM_FOOD: 5, TOOL_PICKAXE_STONE: 1}),
            (MISSION_COLLECT, "Iron Seeker", "Find {} iron ore", 
             lambda: random.randint(3, 10), {ITEM_FOOD: 6, TOOL_AXE_IRON: 1}),
            
            # Survival missions
            (MISSION_SURVIVE, "Night Survivor", "Survive {} seconds at night", 
             lambda: random.randint(180, 300), {ITEM_FOOD: 8, ITEM_COAL: 5}),
            (MISSION_SURVIVE, "Endurance Test", "Survive {} seconds total", 
             lambda: random.randint(600, 1200), {TOOL_SWORD_IRON: 1, ITEM_FOOD: 10}),
            
            # Combat missions
            (MISSION_KILL, "Zombie Slayer", "Kill {} zombies", 
             lambda: random.randint(8, 15), {TOOL_SWORD_STONE: 1, ITEM_FOOD: 6}),
            (MISSION_KILL, "Area Cleaner", "Eliminate {} zombies from the area", 
             lambda: random.randint(12, 25), {TOOL_SWORD_IRON: 1, ITEM_FOOD: 10}),
            
            # Building missions
            (MISSION_BUILD, "Constructor", "Place {} blocks", 
             lambda: random.randint(30, 60), {ITEM_FOOD: 5, ITEM_WOOD: 10}),
            (MISSION_BUILD, "Architect", "Build with {} blocks", 
             lambda: random.randint(50, 100), {TOOL_AXE_IRON: 1, ITEM_FOOD: 8}),
        ]
        
        # Generate 3-5 random missions
        for _ in range(random.randint(3, 5)):
            template = random.choice(mission_templates)
            mission_type, title, desc_template, amount_func, rewards = template
            
            amount = amount_func()
            description = desc_template.format(amount)
            
            mission = Mission(mission_type, title, description, amount, rewards)
            self.available_missions.append(mission)
    
    def get_active_missions(self):
        """Get list of active missions"""
        return self.active_missions
    
    def get_completed_count(self):
        """Get number of completed missions"""
        return len(self.completed_missions)
    
    def render_missions(self, screen, font):
        """Render mission list on screen"""
        y_offset = 50
        
        # Title
        title_text = font.render("MISSIONS", True, WHITE)
        screen.blit(title_text, (SCREEN_WIDTH - 250, y_offset))
        y_offset += 30
        
        # Active missions
        for i, mission in enumerate(self.active_missions):
            if mission.completed:
                color = GREEN
                status = "[COMPLETE]"
            else:
                color = WHITE
                status = f"[{mission.get_progress_text()}]"
            
            # Mission title
            title_text = font.render(f"{mission.title}", True, color)
            screen.blit(title_text, (SCREEN_WIDTH - 250, y_offset))
            y_offset += 20
            
            # Progress
            progress_text = font.render(status, True, color)
            screen.blit(progress_text, (SCREEN_WIDTH - 250, y_offset))
            y_offset += 25
        
        # Completed missions count
        if self.completed_missions:
            completed_text = font.render(f"Completed: {len(self.completed_missions)}", True, GREEN)
            screen.blit(completed_text, (SCREEN_WIDTH - 250, y_offset + 10))
